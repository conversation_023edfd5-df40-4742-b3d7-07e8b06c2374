# 改进PRD-05: 单元测试覆盖

**优先级**: 🟠 长期优先级  
**时间线**: 长期改进（1-2个月内）  
**负责人**: 全栈开发团队  
**预估工时**: 32小时  

---

## 📋 需求背景

### 问题描述
- 项目缺乏单元测试覆盖
- 核心业务逻辑缺乏自动化测试保障
- 重构和功能变更风险较高

### 影响评估
- **质量风险**: 缺乏测试保障，bug风险高
- **重构风险**: 无法安全进行代码重构
- **维护成本**: 手动测试成本高，效率低

---

## 🎯 改进目标

### 主要目标
1. 为核心业务逻辑添加单元测试
2. 建立测试驱动开发流程
3. 达到合理的测试覆盖率

### 成功标准
- [ ] 核心业务逻辑测试覆盖率>80%
- [ ] 关键API接口测试覆盖率>90%
- [ ] 建立自动化测试流程
- [ ] 集成到CI/CD流程

---

## 🛠️ 技术方案

### 1. 测试框架选择

**后端测试: Jest + Supertest**
```bash
cd backend
npm install --save-dev jest @types/jest ts-jest supertest @types/supertest
```

**前端测试: Vitest + Vue Test Utils**
```bash
cd frontend
npm install --save-dev vitest @vue/test-utils jsdom
```

### 2. 测试配置

**后端 Jest 配置 `jest.config.js`**:
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/index.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts']
};
```

**前端 Vitest 配置 `vitest.config.ts`**:
```typescript
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts'
      ]
    }
  }
})
```

### 3. 测试数据库配置

**测试数据库设置**:
```typescript
// tests/setup.ts
import { initDatabase, closeDatabase } from '../src/models/database';
import path from 'path';

// 使用内存数据库进行测试
process.env.DB_PATH = ':memory:';

beforeAll(async () => {
  await initDatabase();
});

afterAll(async () => {
  closeDatabase();
});

// 每个测试前清理数据
beforeEach(async () => {
  // 清理测试数据
});
```

---

## 📝 实施步骤

### 第一阶段：测试环境搭建（8小时）

1. **安装测试依赖**
   ```bash
   # 后端测试环境
   cd backend
   npm install --save-dev jest @types/jest ts-jest supertest @types/supertest
   
   # 前端测试环境
   cd frontend
   npm install --save-dev vitest @vue/test-utils jsdom
   ```

2. **配置测试框架**
   - 创建Jest和Vitest配置文件
   - 设置测试数据库
   - 配置覆盖率报告

3. **创建测试工具**
   ```typescript
   // tests/helpers/testUtils.ts
   export function createMockRequest(data: any) {
     return {
       body: data,
       params: {},
       query: {},
       user: { id: 1, username: 'test' }
     } as any;
   }
   
   export function createMockResponse() {
     const res: any = {};
     res.status = jest.fn().mockReturnValue(res);
     res.json = jest.fn().mockReturnValue(res);
     return res;
   }
   ```

### 第二阶段：核心业务逻辑测试（16小时）

1. **库存管理测试**
   ```typescript
   // tests/utils/inventoryUtils.test.ts
   import { updateInventoryWithMovement } from '../../src/utils/inventoryUtils';
   
   describe('库存管理工具', () => {
     test('应该正确更新库存并记录变动', async () => {
       // 准备测试数据
       const materialId = 1;
       const quantity = 100;
       
       // 执行操作
       await updateInventoryWithMovement(
         'material',
         materialId,
         quantity,
         'in',
         'test',
         1,
         'TEST001',
         '测试入库'
       );
       
       // 验证结果
       // 检查库存是否正确更新
       // 检查变动记录是否正确创建
     });
     
     test('应该正确处理并发库存操作', async () => {
       // 并发测试逻辑
     });
   });
   ```

2. **API控制器测试**
   ```typescript
   // tests/controllers/materialController.test.ts
   import request from 'supertest';
   import app from '../../src/app';
   
   describe('原材料控制器', () => {
     test('POST /api/materials - 应该成功创建原材料', async () => {
       const materialData = {
         code: 'TEST001',
         name: '测试原材料',
         unit: 'kg',
         cost_price: 10.5
       };
       
       const response = await request(app)
         .post('/api/materials')
         .send(materialData)
         .expect(201);
       
       expect(response.body.success).toBe(true);
       expect(response.body.data.id).toBeDefined();
     });
     
     test('POST /api/materials - 应该拒绝重复编码', async () => {
       // 重复编码测试
     });
   });
   ```

3. **数据库操作测试**
   ```typescript
   // tests/models/database.test.ts
   describe('数据库操作', () => {
     test('应该正确处理事务回滚', async () => {
       // 事务测试逻辑
     });
     
     test('应该正确处理并发访问', async () => {
       // 并发测试逻辑
     });
   });
   ```

### 第三阶段：前端组件测试（8小时）

1. **通用组件测试**
   ```typescript
   // tests/components/DataTable.test.ts
   import { mount } from '@vue/test-utils'
   import DataTable from '../../src/components/DataTable.vue'
   
   describe('DataTable组件', () => {
     test('应该正确渲染数据', () => {
       const wrapper = mount(DataTable, {
         props: {
           data: [
             { id: 1, name: '测试数据' }
           ],
           columns: [
             { prop: 'id', label: 'ID' },
             { prop: 'name', label: '名称' }
           ]
         }
       })
       
       expect(wrapper.find('table').exists()).toBe(true)
       expect(wrapper.text()).toContain('测试数据')
     })
     
     test('应该正确处理搜索', async () => {
       // 搜索功能测试
     })
   })
   ```

2. **状态管理测试**
   ```typescript
   // tests/stores/auth.test.ts
   import { setActivePinia, createPinia } from 'pinia'
   import { useAuthStore } from '../../src/stores/auth'
   
   describe('认证状态管理', () => {
     beforeEach(() => {
       setActivePinia(createPinia())
     })
     
     test('应该正确处理登录', async () => {
       const authStore = useAuthStore()
       
       const result = await authStore.login({
         username: 'test',
         password: 'password'
       })
       
       expect(result.success).toBe(true)
       expect(authStore.isLoggedIn).toBe(true)
     })
   })
   ```

---

## 📊 测试覆盖率目标

### 后端覆盖率目标
| 模块 | 行覆盖率 | 函数覆盖率 | 分支覆盖率 |
|------|----------|------------|------------|
| 控制器 | 90% | 95% | 85% |
| 工具函数 | 95% | 100% | 90% |
| 数据库操作 | 85% | 90% | 80% |
| 中间件 | 90% | 95% | 85% |

### 前端覆盖率目标
| 模块 | 行覆盖率 | 函数覆盖率 | 分支覆盖率 |
|------|----------|------------|------------|
| 组件 | 80% | 85% | 75% |
| 状态管理 | 90% | 95% | 85% |
| 工具函数 | 95% | 100% | 90% |
| API调用 | 85% | 90% | 80% |

---

## 📋 测试清单

### 核心业务逻辑测试
- [ ] 库存管理功能
- [ ] 订单处理流程
- [ ] 用户认证授权
- [ ] 数据验证逻辑
- [ ] 事务处理机制

### API接口测试
- [ ] 认证相关API
- [ ] 原材料管理API
- [ ] 产品管理API
- [ ] 订单管理API
- [ ] 库存管理API

### 前端组件测试
- [ ] 通用组件（DataTable、FormDialog等）
- [ ] 业务组件（产品列表、订单表单等）
- [ ] 状态管理（Pinia stores）
- [ ] 路由守卫

### 集成测试
- [ ] 完整业务流程测试
- [ ] 数据库集成测试
- [ ] API集成测试

---

## ✅ 验收标准

### 覆盖率验收
- [ ] 后端核心模块覆盖率>80%
- [ ] 前端核心模块覆盖率>75%
- [ ] 关键API接口覆盖率>90%
- [ ] 无关键业务逻辑遗漏

### 质量验收
- [ ] 所有测试用例通过
- [ ] 测试用例质量良好
- [ ] 测试数据隔离正确
- [ ] 性能测试通过

### 流程验收
- [ ] CI/CD集成正常
- [ ] 测试报告生成正确
- [ ] 团队能够正常使用
- [ ] 文档完整清晰

---

## 📈 持续改进

### 测试质量提升
- 定期review测试用例质量
- 补充边界条件测试
- 优化测试性能

### 流程优化
- 完善测试数据管理
- 优化测试执行速度
- 建立测试最佳实践

### 团队能力建设
- 测试驱动开发培训
- 测试工具使用培训
- 建立测试文化

---

**创建日期**: 2025-08-05  
**最后更新**: 2025-08-05  
**版本**: v1.0
