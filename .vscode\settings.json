{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "typescript.preferences.organizeImportsCollation": "ordinal", "typescript.preferences.organizeImportsIgnoreCase": "auto", "typescript.preferences.organizeImportsNumericCollation": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "eslint.workingDirectories": ["./frontend", "./backend"]}