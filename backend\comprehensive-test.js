const { initDatabase } = require('./dist/models/database');

async function comprehensiveFunctionalTest() {
  console.log('开始执行全面功能测试...');

  try {
    // 初始化数据库
    await initDatabase();

    // 模拟Express请求和响应对象
    function createMockReq(body = {}, params = {}) {
      return { body, params };
    }

    function createMockRes() {
      const res = {
        status: function(code) {
          this.statusCode = code;
          return this;
        },
        json: function(data) {
          this.responseData = data;
          return this;
        },
        statusCode: 200,
        responseData: null
      };
      return res;
    }

    let testResults = {
      passed: 0,
      failed: 0,
      details: []
    };

    function logTest(name, passed, message = '') {
      if (passed) {
        console.log(`✅ ${name}`);
        testResults.passed++;
      } else {
        console.log(`❌ ${name}: ${message}`);
        testResults.failed++;
      }
      testResults.details.push({ name, passed, message });
    }

    // 测试原材料CRUD操作
    console.log('\n=== 原材料CRUD测试 ===');
    
    const { 
      createMaterialNew, 
      updateMaterialNew, 
      deleteMaterialNew, 
      getMaterialNew 
    } = require('./dist/controllers/materialController');

    // 创建测试
    let materialId;
    try {
      const req = createMockReq({
        code: 'CRUD_TEST_MAT',
        name: 'CRUD测试原材料',
        unit: 'kg',
        cost_price: 15.5,
        stock_min: 50,
        stock_max: 500,
        current_stock: 200
      });
      const res = createMockRes();
      await createMaterialNew(req, res);
      
      if (res.responseData?.success && res.responseData?.data?.id) {
        materialId = res.responseData.data.id;
        logTest('原材料创建', true);
      } else {
        logTest('原材料创建', false, '创建失败');
      }
    } catch (error) {
      logTest('原材料创建', false, error.message);
    }

    // 获取测试
    if (materialId) {
      try {
        const req = createMockReq({}, { id: materialId.toString() });
        const res = createMockRes();
        await getMaterialNew(req, res);
        
        logTest('原材料获取', res.responseData?.success === true);
      } catch (error) {
        logTest('原材料获取', false, error.message);
      }
    }

    // 更新测试
    if (materialId) {
      try {
        const req = createMockReq({
          name: 'CRUD测试原材料-已更新',
          cost_price: 20.0
        }, { id: materialId.toString() });
        const res = createMockRes();
        await updateMaterialNew(req, res);
        
        logTest('原材料更新', res.responseData?.success === true);
      } catch (error) {
        logTest('原材料更新', false, error.message);
      }
    }

    // 删除测试
    if (materialId) {
      try {
        const req = createMockReq({}, { id: materialId.toString() });
        const res = createMockRes();
        await deleteMaterialNew(req, res);
        
        logTest('原材料删除', res.responseData?.success === true);
      } catch (error) {
        logTest('原材料删除', false, error.message);
      }
    }

    // 测试产品CRUD操作
    console.log('\n=== 产品CRUD测试 ===');
    
    const { 
      createProductNew, 
      updateProductNew, 
      deleteProductNew, 
      getProductNew 
    } = require('./dist/controllers/productController');

    let productId;
    try {
      const req = createMockReq({
        code: 'CRUD_TEST_PROD',
        name: 'CRUD测试产品',
        unit: 'pcs',
        cost_price: 30.0,
        sale_price: 50.0,
        stock_min: 20,
        stock_max: 200,
        current_stock: 100
      });
      const res = createMockRes();
      await createProductNew(req, res);
      
      if (res.responseData?.success && res.responseData?.data?.id) {
        productId = res.responseData.data.id;
        logTest('产品创建', true);
      } else {
        logTest('产品创建', false, '创建失败');
      }
    } catch (error) {
      logTest('产品创建', false, error.message);
    }

    if (productId) {
      try {
        const req = createMockReq({}, { id: productId.toString() });
        const res = createMockRes();
        await getProductNew(req, res);
        logTest('产品获取', res.responseData?.success === true);
      } catch (error) {
        logTest('产品获取', false, error.message);
      }

      try {
        const req = createMockReq({
          sale_price: 60.0
        }, { id: productId.toString() });
        const res = createMockRes();
        await updateProductNew(req, res);
        logTest('产品更新', res.responseData?.success === true);
      } catch (error) {
        logTest('产品更新', false, error.message);
      }

      try {
        const req = createMockReq({}, { id: productId.toString() });
        const res = createMockRes();
        await deleteProductNew(req, res);
        logTest('产品删除', res.responseData?.success === true);
      } catch (error) {
        logTest('产品删除', false, error.message);
      }
    }

    // 测试客户CRUD操作
    console.log('\n=== 客户CRUD测试 ===');
    
    const { 
      createCustomerNew, 
      updateCustomerNew, 
      deleteCustomerNew, 
      getCustomerNew 
    } = require('./dist/controllers/customerController');

    let customerId;
    try {
      const req = createMockReq({
        code: 'CRUD_TEST_CUST',
        name: 'CRUD测试客户',
        contact_person: '测试联系人',
        phone: '13800000000',
        credit_limit: 50000
      });
      const res = createMockRes();
      await createCustomerNew(req, res);
      
      if (res.responseData?.success && res.responseData?.data?.id) {
        customerId = res.responseData.data.id;
        logTest('客户创建', true);
      } else {
        logTest('客户创建', false, '创建失败');
      }
    } catch (error) {
      logTest('客户创建', false, error.message);
    }

    if (customerId) {
      try {
        const req = createMockReq({}, { id: customerId.toString() });
        const res = createMockRes();
        await getCustomerNew(req, res);
        logTest('客户获取', res.responseData?.success === true);
      } catch (error) {
        logTest('客户获取', false, error.message);
      }

      try {
        const req = createMockReq({
          credit_limit: 80000
        }, { id: customerId.toString() });
        const res = createMockRes();
        await updateCustomerNew(req, res);
        logTest('客户更新', res.responseData?.success === true);
      } catch (error) {
        logTest('客户更新', false, error.message);
      }

      try {
        const req = createMockReq({}, { id: customerId.toString() });
        const res = createMockRes();
        await deleteCustomerNew(req, res);
        logTest('客户删除', res.responseData?.success === true);
      } catch (error) {
        logTest('客户删除', false, error.message);
      }
    }

    // 测试供应商CRUD操作
    console.log('\n=== 供应商CRUD测试 ===');
    
    const { 
      createSupplierNew, 
      updateSupplierNew, 
      deleteSupplierNew, 
      getSupplierNew 
    } = require('./dist/controllers/supplierController');

    let supplierId;
    try {
      const req = createMockReq({
        code: 'CRUD_TEST_SUPP',
        name: 'CRUD测试供应商',
        contact_person: '供应商联系人',
        phone: '13900000000',
        settlement_method: '季结'
      });
      const res = createMockRes();
      await createSupplierNew(req, res);
      
      if (res.responseData?.success && res.responseData?.data?.id) {
        supplierId = res.responseData.data.id;
        logTest('供应商创建', true);
      } else {
        logTest('供应商创建', false, '创建失败');
      }
    } catch (error) {
      logTest('供应商创建', false, error.message);
    }

    if (supplierId) {
      try {
        const req = createMockReq({}, { id: supplierId.toString() });
        const res = createMockRes();
        await getSupplierNew(req, res);
        logTest('供应商获取', res.responseData?.success === true);
      } catch (error) {
        logTest('供应商获取', false, error.message);
      }

      try {
        const req = createMockReq({
          settlement_method: '月结'
        }, { id: supplierId.toString() });
        const res = createMockRes();
        await updateSupplierNew(req, res);
        logTest('供应商更新', res.responseData?.success === true);
      } catch (error) {
        logTest('供应商更新', false, error.message);
      }

      try {
        const req = createMockReq({}, { id: supplierId.toString() });
        const res = createMockRes();
        await deleteSupplierNew(req, res);
        logTest('供应商删除', res.responseData?.success === true);
      } catch (error) {
        logTest('供应商删除', false, error.message);
      }
    }

    // 输出测试结果
    console.log('\n=== 测试结果汇总 ===');
    console.log(`总测试数: ${testResults.passed + testResults.failed}`);
    console.log(`通过: ${testResults.passed}`);
    console.log(`失败: ${testResults.failed}`);
    console.log(`成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(2)}%`);

    if (testResults.failed === 0) {
      console.log('\n🎉 所有功能测试通过！');
    } else {
      console.log('\n⚠️ 部分测试失败，需要检查问题');
    }

  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

comprehensiveFunctionalTest();
