import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import { Product, ProductForm, ProductQuery } from '../types/Product';

// 获取成品列表
export async function getProducts(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const query = req.query as ProductQuery;
    
    const page = parseInt(String(query.page || '1')) || 1;
    const pageSize = parseInt(String(query.pageSize || '10')) || 10;
    const search = query.search || '';
    const status = query.status || 'active';
    
    const offset = (page - 1) * pageSize;
    
    // 构建查询条件
    let whereClause = 'WHERE status = ?';
    const params: any[] = [status];
    
    if (search) {
      whereClause += ' AND (code LIKE ? OR name LIKE ? OR specification LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM products ${whereClause}`;
    const totalResult = await new Promise<{ total: number }>((resolve, reject) => {
      db.get(countSql, params, (err, row: any) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    // 获取分页数据
    const dataSql = `
      SELECT * FROM products 
      ${whereClause} 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...params, pageSize, offset];
    
    const products = await new Promise<Product[]>((resolve, reject) => {
      db.all(dataSql, dataParams, (err, rows: any[]) => {
        if (err) reject(err);
        else resolve(rows as Product[]);
      });
    });
    
    res.json({
      success: true,
      data: {
        products,
        total: totalResult.total,
        page,
        pageSize
      }
    });
  } catch (error) {
    console.error('获取成品列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取成品列表失败'
    });
  }
}

// 创建成品
export async function createProduct(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const productData: ProductForm = req.body;
    
    // 验证必填字段
    if (!productData.code || !productData.name || !productData.unit) {
      return res.status(400).json({
        success: false,
        message: '编码、名称和单位为必填字段'
      });
    }
    
    // 验证价格数值
    if (productData.cost_price < 0 || productData.sale_price < 0) {
      return res.status(400).json({
        success: false,
        message: '价格不能为负数'
      });
    }
    
    // 验证库存数值
    if (productData.stock_min < 0 || productData.stock_max < 0) {
      return res.status(400).json({
        success: false,
        message: '库存数量不能为负数'
      });
    }
    
    if (productData.stock_min > productData.stock_max) {
      return res.status(400).json({
        success: false,
        message: '最小库存不能大于最大库存'
      });
    }
    
    const sql = `
      INSERT INTO products (
        code, name, specification, unit, cost_price, sale_price,
        stock_min, stock_max, current_stock, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      productData.code,
      productData.name,
      productData.specification || '',
      productData.unit,
      productData.cost_price,
      productData.sale_price,
      productData.stock_min,
      productData.stock_max,
      productData.current_stock || 0,
      productData.status || 'active'
    ];
    
    const result = await new Promise<{ lastID: number }>((resolve, reject) => {
      db.run(sql, params, function(err) {
        if (err) reject(err);
        else resolve({ lastID: this.lastID });
      });
    });
    
    res.status(201).json({
      success: true,
      message: '成品创建成功',
      data: { id: result.lastID }
    });
  } catch (error: any) {
    console.error('创建成品失败:', error);
    
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      return res.status(400).json({
        success: false,
        message: '成品编码已存在'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '创建成品失败'
    });
  }
}

// 更新成品
export async function updateProduct(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const id = parseInt(req.params.id);
    const productData: ProductForm = req.body;
    
    // 验证必填字段
    if (!productData.code || !productData.name || !productData.unit) {
      return res.status(400).json({
        success: false,
        message: '编码、名称和单位为必填字段'
      });
    }
    
    // 验证价格数值
    if (productData.cost_price < 0 || productData.sale_price < 0) {
      return res.status(400).json({
        success: false,
        message: '价格不能为负数'
      });
    }
    
    // 验证库存数值
    if (productData.stock_min < 0 || productData.stock_max < 0) {
      return res.status(400).json({
        success: false,
        message: '库存数量不能为负数'
      });
    }
    
    if (productData.stock_min > productData.stock_max) {
      return res.status(400).json({
        success: false,
        message: '最小库存不能大于最大库存'
      });
    }
    
    const sql = `
      UPDATE products SET 
        code = ?, name = ?, specification = ?, unit = ?, 
        cost_price = ?, sale_price = ?, stock_min = ?, stock_max = ?, 
        current_stock = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const params = [
      productData.code,
      productData.name,
      productData.specification || '',
      productData.unit,
      productData.cost_price,
      productData.sale_price,
      productData.stock_min,
      productData.stock_max,
      productData.current_stock || 0,
      productData.status || 'active',
      id
    ];
    
    const result = await new Promise<{ changes: number }>((resolve, reject) => {
      db.run(sql, params, function(err) {
        if (err) reject(err);
        else resolve({ changes: this.changes });
      });
    });
    
    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        message: '成品不存在'
      });
    }
    
    res.json({
      success: true,
      message: '成品更新成功'
    });
  } catch (error: any) {
    console.error('更新成品失败:', error);
    
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      return res.status(400).json({
        success: false,
        message: '成品编码已存在'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '更新成品失败'
    });
  }
}

// 删除成品
export async function deleteProduct(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const id = parseInt(req.params.id);
    
    const sql = 'DELETE FROM products WHERE id = ?';
    
    const result = await new Promise<{ changes: number }>((resolve, reject) => {
      db.run(sql, [id], function(err) {
        if (err) reject(err);
        else resolve({ changes: this.changes });
      });
    });
    
    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        message: '成品不存在'
      });
    }
    
    res.json({
      success: true,
      message: '成品删除成功'
    });
  } catch (error) {
    console.error('删除成品失败:', error);
    res.status(500).json({
      success: false,
      message: '删除成品失败'
    });
  }
}

// 获取单个成品详情
export async function getProductById(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const id = parseInt(req.params.id);
    
    const sql = 'SELECT * FROM products WHERE id = ?';
    
    const product = await new Promise<Product | undefined>((resolve, reject) => {
      db.get(sql, [id], (err, row: any) => {
        if (err) reject(err);
        else resolve(row as Product);
      });
    });
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '成品不存在'
      });
    }
    
    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('获取成品详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取成品详情失败'
    });
  }
}
