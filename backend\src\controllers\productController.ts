import { Request, Response } from "express";
import { getDatabase } from "../models/database";
import { Product, ProductForm, ProductQuery } from "../types/Product";
import { CrudConfig } from "../utils/crudUtils";
import { BaseController } from "./baseController";

// 获取成品列表
export async function getProducts(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const query = req.query as ProductQuery;

    const page = parseInt(String(query.page || "1")) || 1;
    const pageSize = parseInt(String(query.pageSize || "10")) || 10;
    const search = query.search || "";
    const status = query.status || "active";

    const offset = (page - 1) * pageSize;

    // 构建查询条件
    let whereClause = "WHERE status = ?";
    const params: any[] = [status];

    if (search) {
      whereClause +=
        " AND (code LIKE ? OR name LIKE ? OR specification LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM products ${whereClause}`;
    const totalResult = await new Promise<{ total: number }>(
      (resolve, reject) => {
        db.get(countSql, params, (err, row: any) => {
          if (err) reject(err);
          else resolve(row);
        });
      }
    );

    // 获取分页数据
    const dataSql = `
      SELECT * FROM products 
      ${whereClause} 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...params, pageSize, offset];

    const products = await new Promise<Product[]>((resolve, reject) => {
      db.all(dataSql, dataParams, (err, rows: any[]) => {
        if (err) reject(err);
        else resolve(rows as Product[]);
      });
    });

    res.json({
      success: true,
      data: {
        products,
        total: totalResult.total,
        page,
        pageSize,
      },
    });
  } catch (error) {
    console.error("获取成品列表失败:", error);
    res.status(500).json({
      success: false,
      message: "获取成品列表失败",
    });
  }
}

// 创建成品
export async function createProduct(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const productData: ProductForm = req.body;

    // 验证必填字段
    if (!productData.code || !productData.name || !productData.unit) {
      return res.status(400).json({
        success: false,
        message: "编码、名称和单位为必填字段",
      });
    }

    // 验证价格数值
    if (productData.cost_price < 0 || productData.sale_price < 0) {
      return res.status(400).json({
        success: false,
        message: "价格不能为负数",
      });
    }

    // 验证库存数值
    if (productData.stock_min < 0 || productData.stock_max < 0) {
      return res.status(400).json({
        success: false,
        message: "库存数量不能为负数",
      });
    }

    if (productData.stock_min > productData.stock_max) {
      return res.status(400).json({
        success: false,
        message: "最小库存不能大于最大库存",
      });
    }

    const sql = `
      INSERT INTO products (
        code, name, specification, unit, cost_price, sale_price,
        stock_min, stock_max, current_stock, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      productData.code,
      productData.name,
      productData.specification || "",
      productData.unit,
      productData.cost_price,
      productData.sale_price,
      productData.stock_min,
      productData.stock_max,
      productData.current_stock || 0,
      productData.status || "active",
    ];

    const result = await new Promise<{ lastID: number }>((resolve, reject) => {
      db.run(sql, params, function (err) {
        if (err) reject(err);
        else resolve({ lastID: this.lastID });
      });
    });

    res.status(201).json({
      success: true,
      message: "成品创建成功",
      data: { id: result.lastID },
    });
  } catch (error: any) {
    console.error("创建成品失败:", error);

    if (error.code === "SQLITE_CONSTRAINT_UNIQUE") {
      return res.status(400).json({
        success: false,
        message: "成品编码已存在",
      });
    }

    res.status(500).json({
      success: false,
      message: "创建成品失败",
    });
  }
}

// 更新成品
export async function updateProduct(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const id = parseInt(req.params.id);
    const productData: ProductForm = req.body;

    // 验证必填字段
    if (!productData.code || !productData.name || !productData.unit) {
      return res.status(400).json({
        success: false,
        message: "编码、名称和单位为必填字段",
      });
    }

    // 验证价格数值
    if (productData.cost_price < 0 || productData.sale_price < 0) {
      return res.status(400).json({
        success: false,
        message: "价格不能为负数",
      });
    }

    // 验证库存数值
    if (productData.stock_min < 0 || productData.stock_max < 0) {
      return res.status(400).json({
        success: false,
        message: "库存数量不能为负数",
      });
    }

    if (productData.stock_min > productData.stock_max) {
      return res.status(400).json({
        success: false,
        message: "最小库存不能大于最大库存",
      });
    }

    const sql = `
      UPDATE products SET 
        code = ?, name = ?, specification = ?, unit = ?, 
        cost_price = ?, sale_price = ?, stock_min = ?, stock_max = ?, 
        current_stock = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const params = [
      productData.code,
      productData.name,
      productData.specification || "",
      productData.unit,
      productData.cost_price,
      productData.sale_price,
      productData.stock_min,
      productData.stock_max,
      productData.current_stock || 0,
      productData.status || "active",
      id,
    ];

    const result = await new Promise<{ changes: number }>((resolve, reject) => {
      db.run(sql, params, function (err) {
        if (err) reject(err);
        else resolve({ changes: this.changes });
      });
    });

    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        message: "成品不存在",
      });
    }

    res.json({
      success: true,
      message: "成品更新成功",
    });
  } catch (error: any) {
    console.error("更新成品失败:", error);

    if (error.code === "SQLITE_CONSTRAINT_UNIQUE") {
      return res.status(400).json({
        success: false,
        message: "成品编码已存在",
      });
    }

    res.status(500).json({
      success: false,
      message: "更新成品失败",
    });
  }
}

// 删除成品
export async function deleteProduct(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const id = parseInt(req.params.id);

    const sql = "DELETE FROM products WHERE id = ?";

    const result = await new Promise<{ changes: number }>((resolve, reject) => {
      db.run(sql, [id], function (err) {
        if (err) reject(err);
        else resolve({ changes: this.changes });
      });
    });

    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        message: "成品不存在",
      });
    }

    res.json({
      success: true,
      message: "成品删除成功",
    });
  } catch (error) {
    console.error("删除成品失败:", error);
    res.status(500).json({
      success: false,
      message: "删除成品失败",
    });
  }
}

// 获取单个成品详情
export async function getProductById(req: Request, res: Response) {
  try {
    const db = getDatabase();
    const id = parseInt(req.params.id);

    const sql = "SELECT * FROM products WHERE id = ?";

    const product = await new Promise<Product | undefined>(
      (resolve, reject) => {
        db.get(sql, [id], (err, row: any) => {
          if (err) reject(err);
          else resolve(row as Product);
        });
      }
    );

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "成品不存在",
      });
    }

    res.json({
      success: true,
      data: product,
    });
  } catch (error) {
    console.error("获取成品详情失败:", error);
    res.status(500).json({
      success: false,
      message: "获取成品详情失败",
    });
  }
}

/**
 * 新的产品控制器类 - 使用BaseController重构
 */
class ProductController extends BaseController<Product> {
  protected config: CrudConfig = {
    tableName: "products",
    uniqueField: "code",
    entityName: "成品",
  };

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: any): Product {
    // 验证必填字段
    this.validateRequiredFields(data, ["code", "name", "unit"]);

    // 验证价格数值
    this.validateNumericFields(data, ["cost_price", "sale_price"]);

    // 验证库存数值
    this.validateNumericFields(data, [
      "stock_min",
      "stock_max",
      "current_stock",
    ]);

    // 验证库存范围
    if (data.stock_min !== undefined && data.stock_max !== undefined) {
      this.validateRange(data, "stock_min", "stock_max", "库存");
    }

    return {
      code: data.code,
      name: data.name,
      specification: data.specification || "",
      unit: data.unit,
      cost_price: data.cost_price || 0,
      sale_price: data.sale_price || 0,
      stock_min: data.stock_min || 0,
      stock_max: data.stock_max || 0,
      current_stock: data.current_stock || 0,
      status: data.status || "active",
    } as Product;
  }

  /**
   * 验证更新数据
   */
  protected validateUpdateData(data: any): Partial<Product> {
    const updateData: Partial<Product> = {};

    // 只验证提供的字段
    if (data.code !== undefined) updateData.code = data.code;
    if (data.name !== undefined) updateData.name = data.name;
    if (data.specification !== undefined)
      updateData.specification = data.specification;
    if (data.unit !== undefined) updateData.unit = data.unit;

    if (data.cost_price !== undefined) {
      if (data.cost_price < 0) throw new Error("成本价格不能为负数");
      updateData.cost_price = data.cost_price;
    }

    if (data.sale_price !== undefined) {
      if (data.sale_price < 0) throw new Error("销售价格不能为负数");
      updateData.sale_price = data.sale_price;
    }

    if (data.stock_min !== undefined) {
      if (data.stock_min < 0) throw new Error("最小库存不能为负数");
      updateData.stock_min = data.stock_min;
    }

    if (data.stock_max !== undefined) {
      if (data.stock_max < 0) throw new Error("最大库存不能为负数");
      updateData.stock_max = data.stock_max;
    }

    if (data.current_stock !== undefined) {
      if (data.current_stock < 0) throw new Error("当前库存不能为负数");
      updateData.current_stock = data.current_stock;
    }

    if (data.status !== undefined) updateData.status = data.status;

    // 验证库存范围
    if (
      updateData.stock_min !== undefined &&
      updateData.stock_max !== undefined
    ) {
      if (updateData.stock_min > updateData.stock_max) {
        throw new Error("最小库存不能大于最大库存");
      }
    }

    return updateData;
  }
}

// 创建控制器实例
const productController = new ProductController();

// 导出新的方法
export const createProductNew =
  productController.create.bind(productController);
export const updateProductNew =
  productController.update.bind(productController);
export const deleteProductNew =
  productController.delete.bind(productController);
export const getProductNew = productController.getById.bind(productController);
