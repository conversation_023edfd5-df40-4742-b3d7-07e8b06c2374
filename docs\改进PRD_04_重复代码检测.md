# 改进PRD-04: 重复代码检测机制

**优先级**: 🟡 中等优先级  
**时间线**: 中期优化（2-4周内）  
**负责人**: 全栈开发团队  
**预估工时**: 8小时  

---

## 📋 需求背景

### 问题描述
- 缺乏自动化的重复代码检测机制
- 无法及时发现新增的重复代码
- 缺乏重复代码的量化指标和趋势分析

### 影响评估
- **代码质量**: 重复代码持续增长影响代码质量
- **维护成本**: 无法及时发现和处理重复代码
- **开发效率**: 缺乏指导性的重复代码报告

---

## 🎯 改进目标

### 主要目标
1. 建立自动化重复代码检测机制
2. 集成到开发工作流中
3. 提供重复代码分析报告

### 成功标准
- [ ] 配置重复代码检测工具
- [ ] 集成到CI/CD流程
- [ ] 生成定期分析报告
- [ ] 建立重复代码阈值标准

---

## 🛠️ 技术方案

### 1. 工具选择

**主要工具: jscpd**
```bash
# 安装jscpd
npm install --save-dev jscpd
```

**配置文件 `.jscpd.json`**:
```json
{
  "threshold": 5,
  "minLines": 5,
  "minTokens": 50,
  "mode": "mild",
  "reporters": ["html", "json", "console"],
  "output": "./reports/jscpd",
  "ignore": [
    "**/node_modules/**",
    "**/dist/**",
    "**/coverage/**",
    "**/*.min.js",
    "**/*.d.ts"
  ],
  "formats": [
    "typescript",
    "javascript",
    "vue"
  ],
  "absolute": true,
  "gitignore": true
}
```

### 2. 检测脚本配置

**package.json 脚本**:
```json
{
  "scripts": {
    "duplicate-check": "jscpd src/",
    "duplicate-report": "jscpd src/ --reporters html,json --output ./reports/jscpd",
    "duplicate-ci": "jscpd src/ --threshold 3 --exitCode 1"
  }
}
```

### 3. 自定义检测规则

**创建 `scripts/duplicate-check.js`**:
```javascript
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 重复代码检测配置
const config = {
  frontend: {
    path: 'frontend/src',
    threshold: 5,
    minLines: 5,
    formats: ['typescript', 'vue']
  },
  backend: {
    path: 'backend/src',
    threshold: 3,
    minLines: 5,
    formats: ['typescript']
  }
};

// 执行检测
function runDuplicateCheck(projectConfig, projectName) {
  console.log(`\n🔍 检测 ${projectName} 重复代码...`);
  
  const command = `npx jscpd ${projectConfig.path} \
    --threshold ${projectConfig.threshold} \
    --min-lines ${projectConfig.minLines} \
    --reporters json,console \
    --output ./reports/jscpd-${projectName.toLowerCase()}`;
  
  try {
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`❌ ${projectName} 重复代码检测失败:`, error.message);
    return false;
  }
}

// 生成汇总报告
function generateSummaryReport() {
  const reports = [];
  
  ['frontend', 'backend'].forEach(project => {
    const reportPath = `./reports/jscpd-${project}/jscpd-report.json`;
    if (fs.existsSync(reportPath)) {
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      reports.push({ project, ...report });
    }
  });
  
  // 生成HTML汇总报告
  const summaryHtml = generateSummaryHtml(reports);
  fs.writeFileSync('./reports/duplicate-summary.html', summaryHtml);
  
  console.log('\n📊 重复代码检测完成，报告已生成：');
  console.log('- 详细报告: ./reports/jscpd-*/jscpd-report.html');
  console.log('- 汇总报告: ./reports/duplicate-summary.html');
}

// 主函数
function main() {
  // 确保报告目录存在
  if (!fs.existsSync('./reports')) {
    fs.mkdirSync('./reports', { recursive: true });
  }
  
  let allPassed = true;
  
  // 检测前端和后端
  Object.entries(config).forEach(([projectName, projectConfig]) => {
    const passed = runDuplicateCheck(projectConfig, projectName);
    allPassed = allPassed && passed;
  });
  
  // 生成汇总报告
  generateSummaryReport();
  
  // 根据结果设置退出码
  process.exit(allPassed ? 0 : 1);
}

if (require.main === module) {
  main();
}
```

---

## 📝 实施步骤

### 第一阶段：工具配置（3小时）

1. **安装检测工具**
   ```bash
   # 根目录安装
   npm install --save-dev jscpd
   
   # 前端项目安装
   cd frontend
   npm install --save-dev jscpd
   
   # 后端项目安装
   cd backend
   npm install --save-dev jscpd
   ```

2. **配置检测规则**
   - 创建`.jscpd.json`配置文件
   - 设置合理的阈值和忽略规则
   - 配置输出格式和路径

3. **测试基础功能**
   ```bash
   # 运行基础检测
   npm run duplicate-check
   
   # 生成详细报告
   npm run duplicate-report
   ```

### 第二阶段：自定义脚本（3小时）

1. **创建检测脚本**
   - 创建`scripts/duplicate-check.js`
   - 实现分项目检测逻辑
   - 添加报告生成功能

2. **配置npm脚本**
   ```json
   {
     "scripts": {
       "check:duplicates": "node scripts/duplicate-check.js",
       "check:duplicates-ci": "node scripts/duplicate-check.js --ci"
     }
   }
   ```

3. **测试自定义脚本**
   - 验证检测功能正常
   - 确认报告生成正确

### 第三阶段：CI/CD集成（2小时）

1. **GitHub Actions配置**
   ```yaml
   # .github/workflows/code-quality.yml
   name: Code Quality Check
   
   on:
     pull_request:
       branches: [ main, develop ]
   
   jobs:
     duplicate-check:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         
         - name: Setup Node.js
           uses: actions/setup-node@v3
           with:
             node-version: '18'
             
         - name: Install dependencies
           run: |
             npm ci
             cd frontend && npm ci
             cd ../backend && npm ci
             
         - name: Run duplicate code check
           run: npm run check:duplicates-ci
           
         - name: Upload reports
           uses: actions/upload-artifact@v3
           if: always()
           with:
             name: duplicate-code-reports
             path: reports/
   ```

---

## 📊 检测标准配置

### 阈值设置
```json
{
  "frontend": {
    "threshold": 5,
    "minLines": 5,
    "minTokens": 50,
    "maxAllowedPercentage": 10
  },
  "backend": {
    "threshold": 3,
    "minLines": 5,
    "minTokens": 50,
    "maxAllowedPercentage": 8
  }
}
```

### 忽略规则
```json
{
  "ignore": [
    "**/node_modules/**",
    "**/dist/**",
    "**/coverage/**",
    "**/*.min.js",
    "**/*.d.ts",
    "**/test/**/*.spec.ts",
    "**/tests/**/*.test.ts"
  ]
}
```

### 检测范围
- **前端**: Vue组件、TypeScript文件、工具函数
- **后端**: 控制器、服务层、工具函数
- **共享**: 类型定义、常量配置

---

## 📋 报告分析

### 1. 重复代码类型分析
- **完全重复**: 100%相同的代码块
- **结构重复**: 结构相同但变量名不同
- **逻辑重复**: 实现相同逻辑的不同写法

### 2. 重复代码热点
- **控制器层**: CRUD操作重复
- **工具函数**: 相似的数据处理逻辑
- **组件层**: 相似的UI组件结构

### 3. 改进优先级
```javascript
// 优先级评分算法
function calculatePriority(duplicate) {
  let score = 0;
  
  // 重复次数权重
  score += duplicate.occurrences * 10;
  
  // 代码行数权重
  score += duplicate.lines * 2;
  
  // 文件重要性权重
  if (duplicate.files.some(f => f.includes('controller'))) score += 20;
  if (duplicate.files.some(f => f.includes('service'))) score += 15;
  if (duplicate.files.some(f => f.includes('util'))) score += 10;
  
  return score;
}
```

---

## ✅ 验收标准

### 功能验收
- [ ] 重复代码检测工具正常运行
- [ ] 生成HTML和JSON格式报告
- [ ] CI/CD集成正常工作
- [ ] 阈值配置生效

### 质量验收
- [ ] 检测准确率>90%
- [ ] 误报率<5%
- [ ] 报告可读性良好
- [ ] 性能影响可接受

### 集成验收
- [ ] 与现有工作流无冲突
- [ ] 团队成员能够理解报告
- [ ] 自动化流程稳定运行

---

## 📈 使用指南

### 日常使用
```bash
# 快速检测
npm run duplicate-check

# 生成详细报告
npm run duplicate-report

# CI环境检测
npm run duplicate-ci
```

### 报告解读
1. **重复度指标**: 重复代码占总代码的百分比
2. **热点文件**: 重复代码最多的文件
3. **改进建议**: 基于重复模式的具体建议

### 改进流程
1. 定期查看重复代码报告
2. 识别高优先级重复代码
3. 制定重构计划
4. 验证重构效果

---

## 📋 注意事项

### 配置调优
- 根据项目特点调整阈值
- 合理设置忽略规则
- 定期评估检测效果

### 团队协作
- 培训团队成员理解报告
- 建立重复代码处理流程
- 定期讨论改进方案

---

**创建日期**: 2025-08-05  
**最后更新**: 2025-08-05  
**版本**: v1.0
