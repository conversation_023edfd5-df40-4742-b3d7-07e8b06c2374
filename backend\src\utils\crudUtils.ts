import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import { ApiResponse } from '../types';

/**
 * 通用CRUD工具函数
 * 提供标准化的数据库操作和错误处理
 */

// 通用实体接口
export interface BaseEntity {
  id?: number;
  code?: string;
  name?: string;
  status?: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
}

// CRUD操作配置
export interface CrudConfig {
  tableName: string;
  uniqueField?: string; // 用于唯一性检查的字段，通常是'code'
  entityName: string; // 实体名称，用于错误消息
}

/**
 * 检查实体是否存在
 */
export async function checkEntityExists(
  tableName: string,
  field: string,
  value: any,
  excludeId?: number
): Promise<boolean> {
  const db = getDatabase();
  
  let sql = `SELECT 1 FROM ${tableName} WHERE ${field} = ?`;
  const params = [value];
  
  if (excludeId) {
    sql += ' AND id != ?';
    params.push(excludeId);
  }
  
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(!!row);
    });
  });
}

/**
 * 通用创建实体函数
 */
export async function createEntity<T extends BaseEntity>(
  config: CrudConfig,
  data: T
): Promise<number> {
  const db = getDatabase();
  
  // 检查唯一性
  if (config.uniqueField && data[config.uniqueField as keyof T]) {
    const exists = await checkEntityExists(
      config.tableName,
      config.uniqueField,
      data[config.uniqueField as keyof T]
    );
    if (exists) {
      throw new Error(`${config.entityName}${config.uniqueField}已存在`);
    }
  }
  
  // 构建插入SQL
  const fields = Object.keys(data as any).filter(key => data[key as keyof T] !== undefined);
  const placeholders = fields.map(() => '?').join(', ');
  const values = fields.map(key => data[key as keyof T]);
  
  const sql = `INSERT INTO ${config.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
  
  return new Promise((resolve, reject) => {
    db.run(sql, values, function(err) {
      if (err) reject(err);
      else resolve(this.lastID);
    });
  });
}

/**
 * 通用更新实体函数
 */
export async function updateEntity<T extends BaseEntity>(
  config: CrudConfig,
  id: number,
  data: Partial<T>
): Promise<void> {
  const db = getDatabase();
  
  // 检查实体是否存在
  const exists = await checkEntityExists(config.tableName, 'id', id);
  if (!exists) {
    throw new Error(`${config.entityName}不存在`);
  }
  
  // 检查唯一性（排除自身）
  if (config.uniqueField && data[config.uniqueField as keyof T]) {
    const duplicateExists = await checkEntityExists(
      config.tableName,
      config.uniqueField,
      data[config.uniqueField as keyof T],
      id
    );
    if (duplicateExists) {
      throw new Error(`${config.entityName}${config.uniqueField}已存在`);
    }
  }
  
  // 构建更新SQL
  const updateFields: string[] = [];
  const updateValues: any[] = [];
  
  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined) {
      updateFields.push(`${key} = ?`);
      updateValues.push(value);
    }
  });
  
  if (updateFields.length === 0) {
    throw new Error('没有提供更新数据');
  }
  
  updateFields.push('updated_at = CURRENT_TIMESTAMP');
  updateValues.push(id);
  
  const sql = `UPDATE ${config.tableName} SET ${updateFields.join(', ')} WHERE id = ?`;
  
  return new Promise((resolve, reject) => {
    db.run(sql, updateValues, (err) => {
      if (err) reject(err);
      else resolve();
    });
  });
}

/**
 * 通用软删除函数
 */
export async function softDeleteEntity(
  config: CrudConfig,
  id: number
): Promise<void> {
  const db = getDatabase();
  
  // 检查实体是否存在
  const exists = await checkEntityExists(config.tableName, 'id', id);
  if (!exists) {
    throw new Error(`${config.entityName}不存在`);
  }
  
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE ${config.tableName} SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      ['inactive', id],
      (err) => {
        if (err) reject(err);
        else resolve();
      }
    );
  });
}

/**
 * 通用获取实体函数
 */
export async function getEntityById<T>(
  tableName: string,
  id: number
): Promise<T | undefined> {
  const db = getDatabase();
  
  return new Promise((resolve, reject) => {
    db.get(`SELECT * FROM ${tableName} WHERE id = ?`, [id], (err, row) => {
      if (err) reject(err);
      else resolve(row as T);
    });
  });
}

/**
 * 通用错误处理函数
 */
export function handleCrudError(error: any, entityName: string, res: Response): void {
  console.error(`${entityName}操作错误:`, error);
  
  if (error.message.includes('已存在')) {
    res.status(409).json({
      success: false,
      message: error.message
    } as ApiResponse);
  } else if (error.message.includes('不存在')) {
    res.status(404).json({
      success: false,
      message: error.message
    } as ApiResponse);
  } else if (error.message.includes('没有提供')) {
    res.status(400).json({
      success: false,
      message: error.message
    } as ApiResponse);
  } else {
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

/**
 * 通用成功响应函数
 */
export function sendSuccessResponse(
  res: Response,
  message: string,
  data?: any,
  statusCode: number = 200
): void {
  res.status(statusCode).json({
    success: true,
    message,
    data
  } as ApiResponse);
}
