import { Request, Response } from "express";
import { getDatabase } from "../models/database";
import {
  ApiResponse,
  Customer,
  CustomerCreateInput,
  CustomerUpdateInput,
  PaginatedResponse,
} from "../types";
import { CrudConfig } from "../utils/crudUtils";
import { BaseController } from "./baseController";

// 获取客户列表
export async function getCustomers(req: Request, res: Response) {
  try {
    const { page = 1, limit = 10, search = "" } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const db = getDatabase();

    // 构建查询条件
    let whereClause = "WHERE status = 'active'";
    const params: any[] = [];

    if (search) {
      whereClause +=
        " AND (code LIKE ? OR name LIKE ? OR contact_person LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM customers ${whereClause}`;
    const totalResult = await new Promise<{ total: number }>(
      (resolve, reject) => {
        db.get(countQuery, params, (err, row) => {
          if (err) reject(err);
          else resolve(row as { total: number });
        });
      }
    );

    // 获取数据
    const dataQuery = `
      SELECT * FROM customers ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    const customers = await new Promise<Customer[]>((resolve, reject) => {
      db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
        if (err) reject(err);
        else resolve(rows as Customer[]);
      });
    });

    const response: PaginatedResponse<Customer> = {
      data: customers,
      total: totalResult.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(totalResult.total / Number(limit)),
    };

    res.json({
      success: true,
      message: "获取客户列表成功",
      data: response,
    } as ApiResponse<PaginatedResponse<Customer>>);
  } catch (error) {
    console.error("获取客户列表错误:", error);
    res.status(500).json({
      success: false,
      message: "服务器内部错误",
    } as ApiResponse);
  }
}

// 获取单个客户
export async function getCustomer(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const db = getDatabase();

    const customer = await new Promise<Customer | undefined>(
      (resolve, reject) => {
        db.get("SELECT * FROM customers WHERE id = ?", [id], (err, row) => {
          if (err) reject(err);
          else resolve(row as Customer);
        });
      }
    );

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "客户不存在",
      } as ApiResponse);
    }

    res.json({
      success: true,
      message: "获取客户成功",
      data: customer,
    } as ApiResponse<Customer>);
  } catch (error) {
    console.error("获取客户错误:", error);
    res.status(500).json({
      success: false,
      message: "服务器内部错误",
    } as ApiResponse);
  }
}

// 创建客户
export async function createCustomer(req: Request, res: Response) {
  try {
    const {
      code,
      name,
      contact_person = "",
      phone = "",
      address = "",
      credit_limit = 0,
    }: CustomerCreateInput = req.body;

    // 验证必填字段
    if (!code || !name) {
      return res.status(400).json({
        success: false,
        message: "客户编码和名称不能为空",
      } as ApiResponse);
    }

    const db = getDatabase();

    // 检查编码是否已存在
    const existingCustomer = await new Promise<Customer | undefined>(
      (resolve, reject) => {
        db.get("SELECT * FROM customers WHERE code = ?", [code], (err, row) => {
          if (err) reject(err);
          else resolve(row as Customer);
        });
      }
    );

    if (existingCustomer) {
      return res.status(409).json({
        success: false,
        message: "客户编码已存在",
      } as ApiResponse);
    }

    // 创建客户
    const customerId = await new Promise<number>((resolve, reject) => {
      db.run(
        `INSERT INTO customers (code, name, contact_person, phone, address, credit_limit)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [code, name, contact_person, phone, address, credit_limit],
        function (err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });

    res.status(201).json({
      success: true,
      message: "客户创建成功",
      data: { id: customerId },
    } as ApiResponse);
  } catch (error) {
    console.error("创建客户错误:", error);
    res.status(500).json({
      success: false,
      message: "服务器内部错误",
    } as ApiResponse);
  }
}

// 更新客户
export async function updateCustomer(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const updateData: CustomerUpdateInput = req.body;

    const db = getDatabase();

    // 检查客户是否存在
    const existingCustomer = await new Promise<Customer | undefined>(
      (resolve, reject) => {
        db.get("SELECT * FROM customers WHERE id = ?", [id], (err, row) => {
          if (err) reject(err);
          else resolve(row as Customer);
        });
      }
    );

    if (!existingCustomer) {
      return res.status(404).json({
        success: false,
        message: "客户不存在",
      } as ApiResponse);
    }

    // 如果更新编码，检查是否重复
    if (updateData.code && updateData.code !== existingCustomer.code) {
      const duplicateCustomer = await new Promise<Customer | undefined>(
        (resolve, reject) => {
          db.get(
            "SELECT * FROM customers WHERE code = ? AND id != ?",
            [updateData.code, id],
            (err, row) => {
              if (err) reject(err);
              else resolve(row as Customer);
            }
          );
        }
      );

      if (duplicateCustomer) {
        return res.status(409).json({
          success: false,
          message: "客户编码已存在",
        } as ApiResponse);
      }
    }

    // 构建更新语句
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: "没有提供更新数据",
      } as ApiResponse);
    }

    updateFields.push("updated_at = CURRENT_TIMESTAMP");
    updateValues.push(id);

    await new Promise<void>((resolve, reject) => {
      db.run(
        `UPDATE customers SET ${updateFields.join(", ")} WHERE id = ?`,
        updateValues,
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      message: "客户更新成功",
    } as ApiResponse);
  } catch (error) {
    console.error("更新客户错误:", error);
    res.status(500).json({
      success: false,
      message: "服务器内部错误",
    } as ApiResponse);
  }
}

// 删除客户（软删除）
export async function deleteCustomer(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const db = getDatabase();

    // 检查客户是否存在
    const existingCustomer = await new Promise<Customer | undefined>(
      (resolve, reject) => {
        db.get("SELECT * FROM customers WHERE id = ?", [id], (err, row) => {
          if (err) reject(err);
          else resolve(row as Customer);
        });
      }
    );

    if (!existingCustomer) {
      return res.status(404).json({
        success: false,
        message: "客户不存在",
      } as ApiResponse);
    }

    // 软删除
    await new Promise<void>((resolve, reject) => {
      db.run(
        "UPDATE customers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        ["inactive", id],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      message: "客户删除成功",
    } as ApiResponse);
  } catch (error) {
    console.error("删除客户错误:", error);
    res.status(500).json({
      success: false,
      message: "服务器内部错误",
    } as ApiResponse);
  }
}

/**
 * 新的客户控制器类 - 使用BaseController重构
 */
class CustomerController extends BaseController<Customer> {
  protected config: CrudConfig = {
    tableName: "customers",
    uniqueField: "code",
    entityName: "客户",
  };

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: any): Customer {
    // 验证必填字段
    this.validateRequiredFields(data, ["code", "name"]);

    // 验证信用额度
    if (data.credit_limit !== undefined && data.credit_limit < 0) {
      throw new Error("信用额度不能为负数");
    }

    return {
      code: data.code,
      name: data.name,
      contact_person: data.contact_person || "",
      phone: data.phone || "",
      address: data.address || "",
      credit_limit: data.credit_limit || 0,
      status: "active",
    } as Customer;
  }

  /**
   * 验证更新数据
   */
  protected validateUpdateData(data: any): Partial<Customer> {
    const updateData: Partial<Customer> = {};

    // 只验证提供的字段
    if (data.code !== undefined) updateData.code = data.code;
    if (data.name !== undefined) updateData.name = data.name;
    if (data.contact_person !== undefined)
      updateData.contact_person = data.contact_person;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.address !== undefined) updateData.address = data.address;

    if (data.credit_limit !== undefined) {
      if (data.credit_limit < 0) throw new Error("信用额度不能为负数");
      updateData.credit_limit = data.credit_limit;
    }

    if (data.status !== undefined) updateData.status = data.status;

    return updateData;
  }
}

// 创建控制器实例
const customerController = new CustomerController();

// 导出新的方法
export const createCustomerNew =
  customerController.create.bind(customerController);
export const updateCustomerNew =
  customerController.update.bind(customerController);
export const deleteCustomerNew =
  customerController.delete.bind(customerController);
export const getCustomerNew =
  customerController.getById.bind(customerController);
