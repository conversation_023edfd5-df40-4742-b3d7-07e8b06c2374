import { Request, Response } from "express";
import { ApiResponse } from "../types";
import {
  BaseEntity,
  createEntity,
  CrudConfig,
  getEntityById,
  handleCrudError,
  sendSuccessResponse,
  softDeleteEntity,
  updateEntity,
} from "../utils/crudUtils";

/**
 * 基础控制器抽象类
 * 提供通用的CRUD操作方法
 */
export abstract class BaseController<T extends BaseEntity> {
  protected abstract config: CrudConfig;

  /**
   * 验证创建数据 - 子类需要实现
   */
  protected abstract validateCreateData(data: any): T;

  /**
   * 验证更新数据 - 子类需要实现
   */
  protected abstract validateUpdateData(data: any): Partial<T>;

  /**
   * 创建后的额外处理 - 子类可选实现
   */
  protected async afterCreate?(id: number, data: T): Promise<void>;

  /**
   * 更新后的额外处理 - 子类可选实现
   */
  protected async afterUpdate?(id: number, data: Partial<T>): Promise<void>;

  /**
   * 删除前的验证 - 子类可选实现
   */
  protected async beforeDelete?(id: number): Promise<void>;

  /**
   * 通用创建方法
   */
  public async create(req: Request, res: Response): Promise<void> {
    try {
      // 验证数据
      const validatedData = this.validateCreateData(req.body);

      // 创建实体
      const id = await createEntity(this.config, validatedData);

      // 执行创建后处理
      if (this.afterCreate) {
        await this.afterCreate(id, validatedData);
      }

      sendSuccessResponse(
        res,
        `${this.config.entityName}创建成功`,
        { id },
        201
      );
    } catch (error: any) {
      handleCrudError(error, this.config.entityName, res);
    }
  }

  /**
   * 通用更新方法
   */
  public async update(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: "无效的ID参数",
        } as ApiResponse);
        return;
      }

      // 验证数据
      const validatedData = this.validateUpdateData(req.body);

      // 更新实体
      await updateEntity(this.config, id, validatedData);

      // 执行更新后处理
      if (this.afterUpdate) {
        await this.afterUpdate(id, validatedData);
      }

      sendSuccessResponse(res, `${this.config.entityName}更新成功`);
    } catch (error: any) {
      handleCrudError(error, this.config.entityName, res);
    }
  }

  /**
   * 通用删除方法（软删除）
   */
  public async delete(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: "无效的ID参数",
        } as ApiResponse);
        return;
      }

      // 执行删除前验证
      if (this.beforeDelete) {
        await this.beforeDelete(id);
      }

      // 软删除实体
      await softDeleteEntity(this.config, id);

      sendSuccessResponse(res, `${this.config.entityName}删除成功`);
    } catch (error: any) {
      handleCrudError(error, this.config.entityName, res);
    }
  }

  /**
   * 通用获取单个实体方法
   */
  public async getById(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: "无效的ID参数",
        } as ApiResponse);
        return;
      }

      const entity = await getEntityById<T>(this.config.tableName, id);

      if (!entity) {
        res.status(404).json({
          success: false,
          message: `${this.config.entityName}不存在`,
        } as ApiResponse);
        return;
      }

      sendSuccessResponse(res, `获取${this.config.entityName}成功`, entity);
    } catch (error: any) {
      handleCrudError(error, this.config.entityName, res);
    }
  }

  /**
   * 通用字段验证方法
   */
  protected validateRequiredFields(data: any, requiredFields: string[]): void {
    const missingFields = requiredFields.filter((field) => !data[field]);

    if (missingFields.length > 0) {
      throw new Error(`以下字段不能为空: ${missingFields.join(", ")}`);
    }
  }

  /**
   * 通用数值验证方法
   */
  protected validateNumericFields(data: any, numericFields: string[]): void {
    for (const field of numericFields) {
      if (data[field] !== undefined && data[field] < 0) {
        throw new Error(`${field}不能为负数`);
      }
    }
  }

  /**
   * 通用范围验证方法
   */
  protected validateRange(
    data: any,
    minField: string,
    maxField: string,
    fieldName: string
  ): void {
    if (data[minField] !== undefined && data[maxField] !== undefined) {
      if (data[minField] > data[maxField]) {
        throw new Error(`最小${fieldName}不能大于最大${fieldName}`);
      }
    }
  }
}
