const { initDatabase } = require('./dist/models/database');

async function testRefactoredControllers() {
  console.log('开始测试重构后的控制器...');

  try {
    // 初始化数据库
    console.log('初始化数据库...');
    await initDatabase();

    // 模拟Express请求和响应对象
    function createMockReq(body = {}, params = {}) {
      return { body, params };
    }

    function createMockRes() {
      const res = {
        status: function(code) {
          this.statusCode = code;
          return this;
        },
        json: function(data) {
          this.responseData = data;
          console.log(`响应状态: ${this.statusCode || 200}`);
          console.log('响应数据:', JSON.stringify(data, null, 2));
          return this;
        },
        statusCode: 200,
        responseData: null
      };
      return res;
    }

    // 测试原材料控制器
    console.log('\n=== 测试原材料控制器 ===');
    
    try {
      const { createMaterialNew } = require('./dist/controllers/materialController');
      
      const req = createMockReq({
        code: 'TEST_MAT_001',
        name: '测试原材料',
        unit: 'kg',
        cost_price: 10.5,
        stock_min: 100,
        stock_max: 1000,
        current_stock: 500
      });
      
      const res = createMockRes();
      
      await createMaterialNew(req, res);
      
      if (res.responseData && res.responseData.success) {
        console.log('✅ 原材料创建测试通过');
      } else {
        console.log('❌ 原材料创建测试失败');
      }
      
    } catch (error) {
      console.log('❌ 原材料控制器测试出错:', error.message);
    }

    // 测试产品控制器
    console.log('\n=== 测试产品控制器 ===');
    
    try {
      const { createProductNew } = require('./dist/controllers/productController');
      
      const req = createMockReq({
        code: 'TEST_PROD_001',
        name: '测试产品',
        unit: 'pcs',
        cost_price: 50.0,
        sale_price: 80.0,
        stock_min: 50,
        stock_max: 500,
        current_stock: 200
      });
      
      const res = createMockRes();
      
      await createProductNew(req, res);
      
      if (res.responseData && res.responseData.success) {
        console.log('✅ 产品创建测试通过');
      } else {
        console.log('❌ 产品创建测试失败');
      }
      
    } catch (error) {
      console.log('❌ 产品控制器测试出错:', error.message);
    }

    // 测试客户控制器
    console.log('\n=== 测试客户控制器 ===');
    
    try {
      const { createCustomerNew } = require('./dist/controllers/customerController');
      
      const req = createMockReq({
        code: 'TEST_CUST_001',
        name: '测试客户',
        contact_person: '张三',
        phone: '13800138000',
        address: '测试地址',
        credit_limit: 100000
      });
      
      const res = createMockRes();
      
      await createCustomerNew(req, res);
      
      if (res.responseData && res.responseData.success) {
        console.log('✅ 客户创建测试通过');
      } else {
        console.log('❌ 客户创建测试失败');
      }
      
    } catch (error) {
      console.log('❌ 客户控制器测试出错:', error.message);
    }

    // 测试供应商控制器
    console.log('\n=== 测试供应商控制器 ===');
    
    try {
      const { createSupplierNew } = require('./dist/controllers/supplierController');
      
      const req = createMockReq({
        code: 'TEST_SUPP_001',
        name: '测试供应商',
        contact_person: '李四',
        phone: '13900139000',
        address: '供应商地址',
        settlement_method: '月结'
      });
      
      const res = createMockRes();
      
      await createSupplierNew(req, res);
      
      if (res.responseData && res.responseData.success) {
        console.log('✅ 供应商创建测试通过');
      } else {
        console.log('❌ 供应商创建测试失败');
      }
      
    } catch (error) {
      console.log('❌ 供应商控制器测试出错:', error.message);
    }

    console.log('\n=== 测试完成 ===');

  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

testRefactoredControllers();
