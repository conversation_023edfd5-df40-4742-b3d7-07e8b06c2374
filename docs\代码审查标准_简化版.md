# 代码审查标准 - 简化版

## 📋 概述

本文档为30人小型工厂ERP系统制定的**简化版**代码审查标准，注重实用性和开发效率，确保核心质量的同时减少开发负担。

## 🎯 核心原则

- **安全第一**：确保系统安全性和数据完整性
- **功能优先**：优先保证功能正确性和用户体验
- **适度规范**：代码规范以可读性和维护性为主
- **渐进改进**：质量标准随项目成熟度逐步提升

---

## 🔒 必须遵守的标准

### 1. 安全性要求（严格执行）

#### 1.1 身份认证
- [ ] **JWT安全**：Token设置合理过期时间
- [ ] **密码安全**：使用bcrypt加密存储
- [ ] **权限验证**：敏感API必须验证权限

#### 1.2 数据安全
- [ ] **输入验证**：前后端都要验证用户输入
- [ ] **SQL注入防护**：使用参数化查询
- [ ] **敏感信息**：不在日志中记录密码等敏感数据

### 2. 基础功能要求（严格执行）

#### 2.1 错误处理
- [ ] **异步操作**：所有API调用必须有错误处理
- [ ] **用户友好**：错误信息要对用户友好
- [ ] **空值检查**：关键数据要检查null/undefined

#### 2.2 数据完整性
- [ ] **事务处理**：关键业务操作使用数据库事务
- [ ] **数据验证**：重要字段要有格式验证
- [ ] **状态一致性**：确保业务状态逻辑正确
- [ ] **并发安全**：库存操作等关键业务要考虑并发处理

#### 2.3 日志记录（重要）
- [ ] **操作日志**：关键业务操作要记录日志（库存变动、订单状态变更）
- [ ] **错误日志**：系统错误要有详细日志记录
- [ ] **用户操作**：重要操作要记录操作人和操作时间

---

## 📝 推荐遵守的标准

### 1. 代码规范（适度要求）

#### 1.1 命名规范
- [ ] **语义化命名**：变量和函数名要表达清楚用途
- [ ] **统一风格**：项目内保持命名风格一致
- [ ] **避免拼音**：使用英文命名，避免中文拼音

#### 1.2 函数设计
- [ ] **单一职责**：一个函数尽量只做一件事
- [ ] **合理长度**：函数不超过80行（可适当放宽）
- [ ] **参数控制**：参数不超过6个（可适当放宽）

#### 1.3 代码清理（重要）
- [ ] **重复代码检查**：相似的CRUD操作应提取公共函数
- [ ] **无效导入清理**：删除未使用的import语句
- [ ] **调试代码清理**：删除console.log和注释掉的代码

### 2. TypeScript使用

#### 2.1 类型定义
- [ ] **接口定义**：API响应和重要数据结构要有类型
- [ ] **组件Props**：Vue组件的props要有类型定义
- [ ] **避免any**：尽量避免使用any类型

### 3. API设计规范（重要）

#### 3.1 响应格式统一
- [ ] **统一结构**：所有API使用统一的响应格式（success, message, data, error）
- [ ] **状态码正确**：使用正确的HTTP状态码
- [ ] **错误信息**：提供清晰的错误信息和错误码

#### 3.2 配置管理
- [ ] **环境变量**：敏感配置使用环境变量
- [ ] **配置文件**：开发和生产环境配置分离
- [ ] **默认值**：配置项要有合理的默认值

### 4. 组件设计

#### 4.1 Vue组件
- [ ] **组件拆分**：单个组件不超过300行
- [ ] **Props验证**：组件props要有基础验证
- [ ] **事件命名**：自定义事件使用语义化命名

---

## 🛠️ 工具配置（简化版）

### 1. 必需工具
- [ ] **TypeScript**：类型检查
- [ ] **ESLint基础规则**：基本语法检查 + unused-vars规则
- [ ] **Prettier**：代码格式化

### 2. 可选工具
- [ ] **单元测试**：核心业务逻辑可以添加测试
- [ ] **性能监控**：生产环境可以考虑
- [ ] **重复代码检测**：定期使用工具检查（如jscpd）

---

## ✅ 简化审查流程

### 阶段一：自检（开发者）
- [ ] 代码格式化（`npm run format`）
- [ ] ESLint检查（`npm run lint`）
- [ ] TypeScript编译（`npm run type-check`）
- [ ] 清理无效代码（未使用导入、调试代码）
- [ ] 功能自测

### 阶段二：功能验证
- [ ] 核心功能正常工作
- [ ] 错误情况处理正确
- [ ] 用户体验良好

### 阶段三：代码审查（可选）
- [ ] **简单功能**：可以跳过人工审查
- [ ] **核心功能**：需要团队成员审查
- [ ] **安全相关**：必须进行审查

---

## 📊 质量检查清单

### 提交前必检项
- [ ] 功能正常工作
- [ ] 没有明显的安全漏洞
- [ ] 错误处理完整
- [ ] 代码格式化完成
- [ ] API响应格式统一

### 重要功能额外检查
- [ ] 数据库操作正确（事务、并发安全）
- [ ] 权限控制到位
- [ ] 关键操作有日志记录
- [ ] 配置管理规范
- [ ] 性能影响可接受
- [ ] 用户体验良好

---

## 🎯 分阶段质量提升

### 第一阶段（当前）
- 重点：功能实现 + 基础安全
- 标准：本简化版标准

### 第二阶段（功能完善后）
- 重点：代码规范 + 测试覆盖
- 标准：逐步向完整版标准靠拢

### 第三阶段（系统稳定后）
- 重点：性能优化 + 完整质量流程
- 标准：采用完整版审查标准

---

## 💡 实用建议

### 1. 开发效率优先
- 不要为了代码完美而影响功能交付
- 重构可以在功能稳定后进行
- 优先解决用户反馈的问题

### 2. 团队协作
- 代码审查以帮助和学习为目的
- 重大修改前先讨论方案
- 保持代码风格基本一致即可

### 3. 质量平衡
- 核心业务逻辑要严格把关
- 界面细节可以适当放宽
- 安全性问题绝不妥协

---

## 📚 快速参考

### 常用命令
```bash
# 代码检查和格式化
npm run lint        # ESLint检查
npm run format      # Prettier格式化
npm run type-check  # TypeScript检查

# 开发和测试
npm run dev         # 启动开发服务器
npm run build       # 构建生产版本

# 代码清理（建议定期执行）
npx jscpd --min-lines 5 --min-tokens 50 src/  # 检查重复代码
```

### 关键原则
1. **安全性** > **性能** > **代码美观**
2. **功能正确** > **代码完美**
3. **用户体验** > **技术炫技**
4. **团队效率** > **个人偏好**

---

## 🔍 ERP系统特有审查要点

### 1. 数据库并发安全检查
```typescript
// ❌ 错误：库存操作没有事务保护
const updateStock = async (materialId: number, quantity: number) => {
  const current = await getCurrentStock(materialId)
  const newStock = current + quantity
  await updateMaterialStock(materialId, newStock)
}

// ✅ 正确：使用事务保护
const updateStock = async (materialId: number, quantity: number) => {
  db.serialize(() => {
    db.run('BEGIN TRANSACTION')
    // 库存操作
    db.run('COMMIT')
  })
}
```

### 2. 关键操作日志检查
```typescript
// ✅ 库存变动必须记录
await createInventoryMovement({
  item_type: 'material',
  item_id: materialId,
  movement_type: 'in',
  quantity: receivedQuantity,
  reference_type: 'purchase_receipt',
  reference_id: receiptId,
  created_by: userId
})
```

### 3. API响应格式检查
```typescript
// ✅ 统一的响应格式
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}
```

---

## 🔍 重复代码检查指南

### 常见重复模式（ERP系统）
```typescript
// ❌ 重复的API调用
const getMaterials = async () => {
  try {
    const response = await axios.get('/api/materials')
    return response.data
  } catch (error) {
    ElMessage.error('获取原材料失败')
    throw error
  }
}

const getProducts = async () => {
  try {
    const response = await axios.get('/api/products')
    return response.data
  } catch (error) {
    ElMessage.error('获取成品失败')
    throw error
  }
}

// ✅ 提取公共函数
const apiRequest = async (url: string, errorMsg: string) => {
  try {
    const response = await axios.get(url)
    return response.data
  } catch (error) {
    ElMessage.error(errorMsg)
    throw error
  }
}
```

### 检查重点
- [ ] **API调用模式**：相似的增删改查操作
- [ ] **表单验证**：重复的验证规则
- [ ] **数据处理**：相似的数据转换逻辑
- [ ] **组件结构**：相似的列表、表单组件

### 简单检查方法
1. **人工检查**：每周花30分钟浏览代码
2. **工具检查**：月度运行重复代码检测工具
3. **代码审查**：重点关注新增的CRUD功能

---

*本标准适用于项目初期和快速开发阶段，随着项目成熟可逐步提升质量要求。*
