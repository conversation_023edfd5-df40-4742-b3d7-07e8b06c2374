# 部署指南

本文档介绍如何部署ERP进销存管理系统到生产环境。

## 环境要求

### 服务器要求
- 操作系统: Linux (推荐 Ubuntu 20.04+) 或 Windows Server
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 20GB以上可用空间
- Node.js: 16.0.0+
- npm: 8.0.0+

### 网络要求
- 开放端口: 80 (HTTP) 和 443 (HTTPS)
- 域名解析 (可选)

## 部署步骤

### 1. 服务器准备

#### Ubuntu/Debian
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2 (进程管理器)
sudo npm install -g pm2

# 安装Nginx (反向代理)
sudo apt install nginx -y
```

#### CentOS/RHEL
```bash
# 更新系统
sudo yum update -y

# 安装Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 安装PM2
sudo npm install -g pm2

# 安装Nginx
sudo yum install nginx -y
```

### 2. 项目部署

#### 2.1 上传项目文件
```bash
# 创建项目目录
sudo mkdir -p /var/www/erp-system
sudo chown $USER:$USER /var/www/erp-system

# 上传项目文件到服务器
# 可以使用 scp, rsync, git clone 等方式
```

#### 2.2 安装依赖
```bash
cd /var/www/erp-system

# 安装后端依赖
cd backend
npm install --production

# 构建前端
cd ../frontend
npm install
npm run build
```

#### 2.3 配置环境变量
```bash
# 编辑后端环境变量
cd /var/www/erp-system/backend
cp .env .env.production

# 修改生产环境配置
nano .env.production
```

生产环境配置示例：
```env
# 服务器配置
PORT=3000
NODE_ENV=production

# JWT配置 - 请使用强密码
JWT_SECRET=your-super-secure-jwt-secret-key-for-production
JWT_EXPIRES_IN=24h

# 数据库配置
DB_PATH=./data/erp.db
```

#### 2.4 启动后端服务
```bash
cd /var/www/erp-system/backend

# 构建TypeScript
npm run build

# 使用PM2启动服务
pm2 start dist/index.js --name "erp-backend"

# 设置开机自启
pm2 startup
pm2 save
```

### 3. Nginx配置

#### 3.1 创建Nginx配置文件
```bash
sudo nano /etc/nginx/sites-available/erp-system
```

配置内容：
```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名

    # 前端静态文件
    location / {
        root /var/www/erp-system/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 3.2 启用配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/erp-system /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 4. SSL证书配置 (可选但推荐)

#### 4.1 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 5. 数据库备份

#### 5.1 创建备份脚本
```bash
sudo nano /var/www/erp-system/backup.sh
```

脚本内容：
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/erp-system"
DATE=$(date +%Y%m%d_%H%M%S)
DB_PATH="/var/www/erp-system/backend/data/erp.db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp $DB_PATH $BACKUP_DIR/erp_backup_$DATE.db

# 删除7天前的备份
find $BACKUP_DIR -name "erp_backup_*.db" -mtime +7 -delete

echo "Backup completed: erp_backup_$DATE.db"
```

#### 5.2 设置定时备份
```bash
# 设置执行权限
chmod +x /var/www/erp-system/backup.sh

# 添加到crontab
sudo crontab -e
# 添加以下行（每天凌晨2点备份）：
# 0 2 * * * /var/www/erp-system/backup.sh
```

### 6. 监控和日志

#### 6.1 查看PM2状态
```bash
# 查看进程状态
pm2 status

# 查看日志
pm2 logs erp-backend

# 重启服务
pm2 restart erp-backend
```

#### 6.2 查看Nginx日志
```bash
# 访问日志
sudo tail -f /var/log/nginx/access.log

# 错误日志
sudo tail -f /var/log/nginx/error.log
```

### 7. 安全建议

#### 7.1 防火墙配置
```bash
# 启用UFW防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 查看状态
sudo ufw status
```

#### 7.2 定期更新
```bash
# 定期更新系统
sudo apt update && sudo apt upgrade -y

# 更新Node.js依赖
cd /var/www/erp-system/backend
npm audit fix
```

## 故障排除

### 常见问题

1. **后端服务无法启动**
   - 检查端口是否被占用: `netstat -tlnp | grep 3000`
   - 查看PM2日志: `pm2 logs erp-backend`
   - 检查环境变量配置

2. **前端页面无法访问**
   - 检查Nginx配置: `sudo nginx -t`
   - 查看Nginx日志: `sudo tail -f /var/log/nginx/error.log`
   - 确认静态文件路径正确

3. **数据库连接失败**
   - 检查数据库文件权限
   - 确认数据库路径配置正确
   - 查看后端日志

### 性能优化

1. **启用Gzip压缩**
```nginx
# 在Nginx配置中添加
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

2. **设置缓存头**
```nginx
# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 维护计划

- 每日: 检查系统状态和日志
- 每周: 检查备份完整性
- 每月: 更新系统和依赖
- 每季度: 安全审计和性能评估
