import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import { Supplier, SupplierCreateInput, SupplierUpdateInput, ApiResponse, PaginatedResponse } from '../types';

// 获取供应商列表
export async function getSuppliers(req: Request, res: Response) {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    
    const db = getDatabase();
    
    // 构建查询条件
    let whereClause = "WHERE status = 'active'";
    const params: any[] = [];
    
    if (search) {
      whereClause += " AND (code LIKE ? OR name LIKE ? OR contact_person LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    
    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM suppliers ${whereClause}`;
    const totalResult = await new Promise<{ total: number }>((resolve, reject) => {
      db.get(countQuery, params, (err, row) => {
        if (err) reject(err);
        else resolve(row as { total: number });
      });
    });
    
    // 获取数据
    const dataQuery = `
      SELECT * FROM suppliers ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    const suppliers = await new Promise<Supplier[]>((resolve, reject) => {
      db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
        if (err) reject(err);
        else resolve(rows as Supplier[]);
      });
    });
    
    const response: PaginatedResponse<Supplier> = {
      data: suppliers,
      total: totalResult.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(totalResult.total / Number(limit))
    };
    
    res.json({
      success: true,
      message: '获取供应商列表成功',
      data: response
    } as ApiResponse<PaginatedResponse<Supplier>>);
    
  } catch (error) {
    console.error('获取供应商列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 获取单个供应商
export async function getSupplier(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const db = getDatabase();
    
    const supplier = await new Promise<Supplier | undefined>((resolve, reject) => {
      db.get('SELECT * FROM suppliers WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row as Supplier);
      });
    });
    
    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: '供应商不存在'
      } as ApiResponse);
    }
    
    res.json({
      success: true,
      message: '获取供应商成功',
      data: supplier
    } as ApiResponse<Supplier>);
    
  } catch (error) {
    console.error('获取供应商错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 创建供应商
export async function createSupplier(req: Request, res: Response) {
  try {
    const {
      code,
      name,
      contact_person = '',
      phone = '',
      address = '',
      settlement_method = ''
    }: SupplierCreateInput = req.body;
    
    // 验证必填字段
    if (!code || !name) {
      return res.status(400).json({
        success: false,
        message: '供应商编码和名称不能为空'
      } as ApiResponse);
    }
    
    const db = getDatabase();
    
    // 检查编码是否已存在
    const existingSupplier = await new Promise<Supplier | undefined>((resolve, reject) => {
      db.get('SELECT * FROM suppliers WHERE code = ?', [code], (err, row) => {
        if (err) reject(err);
        else resolve(row as Supplier);
      });
    });
    
    if (existingSupplier) {
      return res.status(409).json({
        success: false,
        message: '供应商编码已存在'
      } as ApiResponse);
    }
    
    // 创建供应商
    const supplierId = await new Promise<number>((resolve, reject) => {
      db.run(
        `INSERT INTO suppliers (code, name, contact_person, phone, address, settlement_method)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [code, name, contact_person, phone, address, settlement_method],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });
    
    res.status(201).json({
      success: true,
      message: '供应商创建成功',
      data: { id: supplierId }
    } as ApiResponse);
    
  } catch (error) {
    console.error('创建供应商错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 更新供应商
export async function updateSupplier(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const updateData: SupplierUpdateInput = req.body;
    
    const db = getDatabase();
    
    // 检查供应商是否存在
    const existingSupplier = await new Promise<Supplier | undefined>((resolve, reject) => {
      db.get('SELECT * FROM suppliers WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row as Supplier);
      });
    });
    
    if (!existingSupplier) {
      return res.status(404).json({
        success: false,
        message: '供应商不存在'
      } as ApiResponse);
    }
    
    // 如果更新编码，检查是否重复
    if (updateData.code && updateData.code !== existingSupplier.code) {
      const duplicateSupplier = await new Promise<Supplier | undefined>((resolve, reject) => {
        db.get('SELECT * FROM suppliers WHERE code = ? AND id != ?', [updateData.code, id], (err, row) => {
          if (err) reject(err);
          else resolve(row as Supplier);
        });
      });
      
      if (duplicateSupplier) {
        return res.status(409).json({
          success: false,
          message: '供应商编码已存在'
        } as ApiResponse);
      }
    }
    
    // 构建更新语句
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    });
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有提供更新数据'
      } as ApiResponse);
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);
    
    await new Promise<void>((resolve, reject) => {
      db.run(
        `UPDATE suppliers SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues,
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
    
    res.json({
      success: true,
      message: '供应商更新成功'
    } as ApiResponse);
    
  } catch (error) {
    console.error('更新供应商错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 删除供应商（软删除）
export async function deleteSupplier(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const db = getDatabase();
    
    // 检查供应商是否存在
    const existingSupplier = await new Promise<Supplier | undefined>((resolve, reject) => {
      db.get('SELECT * FROM suppliers WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row as Supplier);
      });
    });
    
    if (!existingSupplier) {
      return res.status(404).json({
        success: false,
        message: '供应商不存在'
      } as ApiResponse);
    }
    
    // 软删除
    await new Promise<void>((resolve, reject) => {
      db.run(
        'UPDATE suppliers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['inactive', id],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
    
    res.json({
      success: true,
      message: '供应商删除成功'
    } as ApiResponse);
    
  } catch (error) {
    console.error('删除供应商错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}
