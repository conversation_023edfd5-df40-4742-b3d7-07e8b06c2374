# 改进PRD-06: 性能监控优化

**优先级**: 🟠 长期优先级  
**时间线**: 长期改进（1-2个月内）  
**负责人**: 全栈开发团队  
**预估工时**: 24小时  

---

## 📋 需求背景

### 问题描述
- 缺乏API响应时间监控
- 数据库查询性能无法量化
- 缺乏系统性能趋势分析
- 无法及时发现性能瓶颈

### 影响评估
- **用户体验**: 性能问题影响用户体验
- **系统稳定性**: 无法预防性能相关故障
- **运维成本**: 缺乏性能数据支持运维决策

---

## 🎯 改进目标

### 主要目标
1. 建立API响应时间监控
2. 实现数据库查询性能分析
3. 添加系统资源监控
4. 建立性能预警机制

### 成功标准
- [ ] API响应时间监控覆盖率100%
- [ ] 数据库慢查询检测机制
- [ ] 性能数据可视化展示
- [ ] 性能预警通知机制

---

## 🛠️ 技术方案

### 1. API性能监控

**中间件实现**:
```typescript
// src/middleware/performance.ts
import { Request, Response, NextFunction } from 'express';

interface PerformanceMetrics {
  endpoint: string;
  method: string;
  responseTime: number;
  statusCode: number;
  timestamp: Date;
  userId?: number;
}

export function performanceMonitor(req: Request, res: Response, next: NextFunction) {
  const startTime = Date.now();
  
  // 记录原始的res.end方法
  const originalEnd = res.end;
  
  res.end = function(chunk?: any, encoding?: any) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // 记录性能指标
    const metrics: PerformanceMetrics = {
      endpoint: req.route?.path || req.path,
      method: req.method,
      responseTime,
      statusCode: res.statusCode,
      timestamp: new Date(),
      userId: (req as any).user?.id
    };
    
    // 异步记录到数据库
    recordPerformanceMetrics(metrics).catch(console.error);
    
    // 检查是否需要预警
    if (responseTime > 5000) { // 5秒预警
      sendPerformanceAlert(metrics).catch(console.error);
    }
    
    // 调用原始的end方法
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
}

async function recordPerformanceMetrics(metrics: PerformanceMetrics) {
  // 记录到数据库或日志系统
  console.log(`API Performance: ${metrics.method} ${metrics.endpoint} - ${metrics.responseTime}ms`);
}

async function sendPerformanceAlert(metrics: PerformanceMetrics) {
  // 发送性能预警
  console.warn(`Performance Alert: ${metrics.endpoint} took ${metrics.responseTime}ms`);
}
```

### 2. 数据库性能监控

**查询性能分析**:
```typescript
// src/utils/dbPerformance.ts
import { getDatabase } from '../models/database';

interface QueryMetrics {
  sql: string;
  params: any[];
  executionTime: number;
  timestamp: Date;
  stackTrace?: string;
}

export class DatabasePerformanceMonitor {
  private static instance: DatabasePerformanceMonitor;
  private slowQueryThreshold = 1000; // 1秒
  
  static getInstance(): DatabasePerformanceMonitor {
    if (!this.instance) {
      this.instance = new DatabasePerformanceMonitor();
    }
    return this.instance;
  }
  
  wrapDatabaseQuery<T>(
    queryFunction: () => Promise<T>,
    sql: string,
    params: any[] = []
  ): Promise<T> {
    const startTime = Date.now();
    
    return queryFunction()
      .then(result => {
        const executionTime = Date.now() - startTime;
        this.recordQueryMetrics(sql, params, executionTime);
        return result;
      })
      .catch(error => {
        const executionTime = Date.now() - startTime;
        this.recordQueryMetrics(sql, params, executionTime, error.stack);
        throw error;
      });
  }
  
  private recordQueryMetrics(
    sql: string,
    params: any[],
    executionTime: number,
    stackTrace?: string
  ) {
    const metrics: QueryMetrics = {
      sql,
      params,
      executionTime,
      timestamp: new Date(),
      stackTrace
    };
    
    // 记录所有查询
    this.logQueryMetrics(metrics);
    
    // 慢查询预警
    if (executionTime > this.slowQueryThreshold) {
      this.handleSlowQuery(metrics);
    }
  }
  
  private logQueryMetrics(metrics: QueryMetrics) {
    console.log(`DB Query: ${metrics.sql} - ${metrics.executionTime}ms`);
  }
  
  private handleSlowQuery(metrics: QueryMetrics) {
    console.warn(`Slow Query Alert: ${metrics.sql} took ${metrics.executionTime}ms`);
    // 可以发送到监控系统或日志聚合服务
  }
}
```

### 3. 系统资源监控

**资源监控服务**:
```typescript
// src/services/systemMonitor.ts
import os from 'os';
import process from 'process';

interface SystemMetrics {
  timestamp: Date;
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    usage: number;
  };
  process: {
    memoryUsage: NodeJS.MemoryUsage;
    uptime: number;
  };
}

export class SystemMonitor {
  private metricsInterval: NodeJS.Timeout | null = null;
  
  start(intervalMs: number = 60000) { // 默认1分钟
    this.metricsInterval = setInterval(() => {
      this.collectMetrics();
    }, intervalMs);
  }
  
  stop() {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }
  }
  
  private collectMetrics() {
    const metrics: SystemMetrics = {
      timestamp: new Date(),
      cpu: {
        usage: this.getCpuUsage(),
        loadAverage: os.loadavg()
      },
      memory: {
        used: os.totalmem() - os.freemem(),
        total: os.totalmem(),
        usage: (os.totalmem() - os.freemem()) / os.totalmem()
      },
      process: {
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime()
      }
    };
    
    this.recordSystemMetrics(metrics);
    this.checkAlerts(metrics);
  }
  
  private getCpuUsage(): number {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times];
      }
      totalIdle += cpu.times.idle;
    });
    
    return 100 - (totalIdle / totalTick) * 100;
  }
  
  private recordSystemMetrics(metrics: SystemMetrics) {
    console.log(`System Metrics: CPU ${metrics.cpu.usage.toFixed(2)}%, Memory ${(metrics.memory.usage * 100).toFixed(2)}%`);
  }
  
  private checkAlerts(metrics: SystemMetrics) {
    // CPU使用率预警
    if (metrics.cpu.usage > 80) {
      console.warn(`High CPU Usage Alert: ${metrics.cpu.usage.toFixed(2)}%`);
    }
    
    // 内存使用率预警
    if (metrics.memory.usage > 0.85) {
      console.warn(`High Memory Usage Alert: ${(metrics.memory.usage * 100).toFixed(2)}%`);
    }
  }
}
```

### 4. 前端性能监控

**前端性能指标收集**:
```typescript
// frontend/src/utils/performance.ts
interface PagePerformance {
  page: string;
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  timestamp: Date;
}

export class FrontendPerformanceMonitor {
  static collectPageMetrics(pageName: string) {
    if (typeof window === 'undefined') return;
    
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paint = performance.getEntriesByType('paint');
        
        const metrics: PagePerformance = {
          page: pageName,
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime,
          timestamp: new Date()
        };
        
        this.sendMetrics(metrics);
      }, 0);
    });
  }
  
  static collectApiMetrics(url: string, method: string, startTime: number, endTime: number, status: number) {
    const metrics = {
      type: 'api',
      url,
      method,
      responseTime: endTime - startTime,
      status,
      timestamp: new Date()
    };
    
    this.sendMetrics(metrics);
  }
  
  private static sendMetrics(metrics: any) {
    // 发送到后端性能监控接口
    fetch('/api/performance/metrics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(metrics)
    }).catch(console.error);
  }
}
```

---

## 📝 实施步骤

### 第一阶段：基础监控搭建（8小时）

1. **API性能监控**
   - 创建性能监控中间件
   - 集成到Express应用
   - 配置性能指标记录

2. **数据库性能监控**
   - 创建数据库性能监控类
   - 包装现有数据库查询
   - 配置慢查询检测

3. **基础测试**
   - 验证监控数据收集
   - 测试性能指标准确性

### 第二阶段：系统监控扩展（8小时）

1. **系统资源监控**
   - 实现系统指标收集
   - 配置监控间隔和阈值
   - 添加预警机制

2. **前端性能监控**
   - 实现页面性能收集
   - 集成API调用监控
   - 配置数据上报

3. **监控数据存储**
   - 设计性能数据表结构
   - 实现数据持久化
   - 配置数据清理策略

### 第三阶段：可视化和预警（8小时）

1. **性能数据可视化**
   - 创建性能监控页面
   - 实现图表展示
   - 添加实时监控

2. **预警机制完善**
   - 配置多级预警阈值
   - 实现预警通知
   - 添加预警历史记录

3. **性能优化建议**
   - 分析性能瓶颈
   - 生成优化建议
   - 建立性能基线

---

## 📊 监控指标体系

### API性能指标
- **响应时间**: 平均、P95、P99响应时间
- **吞吐量**: QPS、并发数
- **错误率**: 4xx、5xx错误比例
- **可用性**: 服务可用率

### 数据库性能指标
- **查询时间**: 平均查询时间、慢查询数量
- **连接数**: 活跃连接数、连接池使用率
- **锁等待**: 锁等待时间、死锁次数
- **缓存命中率**: 查询缓存效率

### 系统资源指标
- **CPU**: 使用率、负载均衡
- **内存**: 使用率、内存泄漏检测
- **磁盘**: I/O使用率、存储空间
- **网络**: 带宽使用、连接数

### 前端性能指标
- **页面加载**: 首屏时间、完全加载时间
- **用户体验**: FCP、LCP、CLS指标
- **资源加载**: 静态资源加载时间
- **API调用**: 前端API调用性能

---

## ✅ 验收标准

### 功能验收
- [ ] API性能监控正常工作
- [ ] 数据库性能监控有效
- [ ] 系统资源监控准确
- [ ] 前端性能数据收集完整

### 数据质量验收
- [ ] 性能数据准确性>95%
- [ ] 监控覆盖率100%
- [ ] 数据存储稳定可靠
- [ ] 预警机制及时有效

### 用户体验验收
- [ ] 监控界面友好易用
- [ ] 性能报告清晰明了
- [ ] 预警通知及时准确
- [ ] 优化建议实用有效

---

## 📈 性能优化建议

### 基于监控数据的优化
1. **API优化**: 识别慢接口并优化
2. **数据库优化**: 优化慢查询和索引
3. **缓存策略**: 基于访问模式优化缓存
4. **资源优化**: 优化系统资源使用

### 持续改进流程
1. **定期分析**: 每周分析性能趋势
2. **阈值调整**: 根据业务增长调整阈值
3. **优化验证**: 验证优化效果
4. **经验总结**: 建立性能优化知识库

---

**创建日期**: 2025-08-05  
**最后更新**: 2025-08-05  
**版本**: v1.0
