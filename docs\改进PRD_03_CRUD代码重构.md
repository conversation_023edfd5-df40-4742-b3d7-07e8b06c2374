# 改进PRD-03: CRUD代码重构优化

**优先级**: 🟡 中等优先级  
**时间线**: 中期优化（2-4周内）  
**负责人**: 后端开发团队  
**预估工时**: 16小时  

---

## 📋 需求背景

### 问题描述
- 多个控制器中存在相似的CRUD操作逻辑
- 创建、更新、删除操作有重复的验证和错误处理代码
- 代码维护成本高，修改时需要同步多个文件

### 重复模式分析
```typescript
// 发现的重复模式
const createXXX = async (req: Request, res: Response) => {
  // 1. 验证必填字段
  // 2. 检查编码是否已存在  
  // 3. 创建记录
  // 4. 返回统一响应格式
}
```

### 影响评估
- **维护成本**: 修改逻辑需要同步多个文件
- **代码质量**: 重复代码降低代码质量
- **开发效率**: 新增CRUD功能需要重复编写相同逻辑

---

## 🎯 改进目标

### 主要目标
1. 提取通用CRUD操作函数
2. 减少代码重复，提高可维护性
3. 建立标准化的CRUD操作模式

### 成功标准
- [ ] 创建通用CRUD工具函数
- [ ] 重构现有控制器使用公共函数
- [ ] 代码重复率降低60%以上
- [ ] 功能测试全部通过

---

## 🛠️ 技术方案

### 1. 通用CRUD工具设计

**创建 `src/utils/crudUtils.ts`**:
```typescript
import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import { ApiResponse } from '../types';

// 通用创建实体函数
export async function createEntity<T>(
  tableName: string,
  data: T,
  uniqueField?: string,
  uniqueValue?: any
): Promise<number> {
  const db = getDatabase();
  
  // 检查唯一性
  if (uniqueField && uniqueValue) {
    const existing = await checkEntityExists(tableName, uniqueField, uniqueValue);
    if (existing) {
      throw new Error(`${uniqueField}已存在`);
    }
  }
  
  // 构建插入SQL
  const fields = Object.keys(data as any);
  const placeholders = fields.map(() => '?').join(', ');
  const values = Object.values(data as any);
  
  const sql = `INSERT INTO ${tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
  
  return new Promise((resolve, reject) => {
    db.run(sql, values, function(err) {
      if (err) reject(err);
      else resolve(this.lastID);
    });
  });
}

// 通用更新实体函数
export async function updateEntity<T>(
  tableName: string,
  id: number,
  data: Partial<T>,
  uniqueField?: string,
  uniqueValue?: any
): Promise<void> {
  const db = getDatabase();
  
  // 检查实体是否存在
  const exists = await checkEntityExists(tableName, 'id', id);
  if (!exists) {
    throw new Error('记录不存在');
  }
  
  // 检查唯一性（排除自身）
  if (uniqueField && uniqueValue) {
    const existing = await checkEntityExists(tableName, uniqueField, uniqueValue, id);
    if (existing) {
      throw new Error(`${uniqueField}已存在`);
    }
  }
  
  // 构建更新SQL
  const fields = Object.keys(data as any);
  const setClause = fields.map(field => `${field} = ?`).join(', ');
  const values = [...Object.values(data as any), id];
  
  const sql = `UPDATE ${tableName} SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
  
  return new Promise((resolve, reject) => {
    db.run(sql, values, (err) => {
      if (err) reject(err);
      else resolve();
    });
  });
}

// 检查实体是否存在
async function checkEntityExists(
  tableName: string,
  field: string,
  value: any,
  excludeId?: number
): Promise<boolean> {
  const db = getDatabase();
  
  let sql = `SELECT 1 FROM ${tableName} WHERE ${field} = ?`;
  const params = [value];
  
  if (excludeId) {
    sql += ' AND id != ?';
    params.push(excludeId);
  }
  
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(!!row);
    });
  });
}
```

### 2. 通用控制器基类

**创建 `src/controllers/baseController.ts`**:
```typescript
import { Request, Response } from 'express';
import { createEntity, updateEntity } from '../utils/crudUtils';
import { ApiResponse } from '../types';

export abstract class BaseController {
  protected abstract tableName: string;
  protected abstract uniqueField?: string;
  
  // 通用创建方法
  protected async handleCreate(
    req: Request,
    res: Response,
    data: any,
    successMessage: string = '创建成功'
  ) {
    try {
      const uniqueValue = this.uniqueField ? data[this.uniqueField] : undefined;
      
      const id = await createEntity(
        this.tableName,
        data,
        this.uniqueField,
        uniqueValue
      );
      
      res.status(201).json({
        success: true,
        message: successMessage,
        data: { id }
      } as ApiResponse);
      
    } catch (error: any) {
      console.error(`创建${this.tableName}错误:`, error);
      
      if (error.message.includes('已存在')) {
        res.status(409).json({
          success: false,
          message: error.message
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          message: '服务器内部错误'
        } as ApiResponse);
      }
    }
  }
  
  // 通用更新方法
  protected async handleUpdate(
    req: Request,
    res: Response,
    id: number,
    data: any,
    successMessage: string = '更新成功'
  ) {
    try {
      const uniqueValue = this.uniqueField ? data[this.uniqueField] : undefined;
      
      await updateEntity(
        this.tableName,
        id,
        data,
        this.uniqueField,
        uniqueValue
      );
      
      res.json({
        success: true,
        message: successMessage
      } as ApiResponse);
      
    } catch (error: any) {
      console.error(`更新${this.tableName}错误:`, error);
      
      if (error.message.includes('不存在')) {
        res.status(404).json({
          success: false,
          message: error.message
        } as ApiResponse);
      } else if (error.message.includes('已存在')) {
        res.status(409).json({
          success: false,
          message: error.message
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          message: '服务器内部错误'
        } as ApiResponse);
      }
    }
  }
}
```

---

## 📝 实施步骤

### 第一阶段：工具函数开发（6小时）

1. **创建通用工具**
   - 创建`src/utils/crudUtils.ts`
   - 实现通用CRUD操作函数
   - 添加完整的错误处理

2. **创建基础控制器**
   - 创建`src/controllers/baseController.ts`
   - 实现通用控制器方法
   - 定义标准化接口

3. **单元测试**
   - 为工具函数编写单元测试
   - 验证各种边界情况

### 第二阶段：控制器重构（8小时）

1. **重构材料控制器**
   ```typescript
   // 重构前
   export async function createMaterial(req: Request, res: Response) {
     // 50行重复代码
   }
   
   // 重构后
   class MaterialController extends BaseController {
     protected tableName = 'materials';
     protected uniqueField = 'code';
     
     async create(req: Request, res: Response) {
       const data = this.validateMaterialData(req.body);
       await this.handleCreate(req, res, data, '原材料创建成功');
     }
   }
   ```

2. **重构其他控制器**
   - 产品控制器
   - 客户控制器
   - 供应商控制器

### 第三阶段：测试验证（2小时）

1. **功能测试**
   - 验证所有CRUD操作正常
   - 测试错误处理逻辑
   - 确保响应格式一致

2. **性能测试**
   - 对比重构前后性能
   - 确保无性能退化

---

## 📋 重构清单

### 需要重构的控制器
- [ ] materialController.ts
- [ ] productController.ts
- [ ] customerController.ts
- [ ] supplierController.ts
- [ ] purchaseOrderController.ts
- [ ] salesOrderController.ts

### 提取的公共功能
- [ ] 实体创建逻辑
- [ ] 实体更新逻辑
- [ ] 唯一性检查
- [ ] 错误处理
- [ ] 响应格式化

### 保留的特殊逻辑
- [ ] 业务特定验证
- [ ] 复杂的关联操作
- [ ] 特殊的数据处理

---

## ✅ 验收标准

### 功能验收
- [ ] 所有CRUD操作功能正常
- [ ] 错误处理逻辑正确
- [ ] 响应格式保持一致
- [ ] 数据验证规则不变

### 代码质量验收
- [ ] 代码重复率降低60%以上
- [ ] 新增代码行数减少30%以上
- [ ] ESLint检查通过
- [ ] 单元测试覆盖率>80%

### 性能验收
- [ ] API响应时间无明显增加
- [ ] 内存使用无明显增加
- [ ] 数据库查询次数不增加

---

## 📊 预期效果

### 代码质量提升
- 减少重复代码60%以上
- 提高代码可维护性
- 统一错误处理逻辑

### 开发效率提升
- 新增CRUD功能开发时间减少50%
- 减少bug修复时间
- 提高代码审查效率

### 维护成本降低
- 修改逻辑只需修改一处
- 减少测试工作量
- 降低新人学习成本

---

## 📋 注意事项

### 风险控制
1. **功能回归**: 重构过程中要保证功能不变
2. **性能影响**: 抽象层不应显著影响性能
3. **复杂度**: 避免过度抽象导致代码复杂

### 最佳实践
1. **渐进式重构**: 一次重构一个控制器
2. **充分测试**: 每次重构后进行完整测试
3. **文档更新**: 及时更新开发文档

---

## 📈 后续优化

### 短期优化
- 完善错误处理机制
- 添加更多通用功能
- 优化性能

### 长期规划
- 考虑引入ORM框架
- 建立更完善的数据访问层
- 实现更高级的抽象

---

**创建日期**: 2025-08-05  
**最后更新**: 2025-08-05  
**版本**: v1.0
