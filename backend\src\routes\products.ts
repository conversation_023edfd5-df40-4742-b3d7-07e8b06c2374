import express from 'express';
import { 
  getProducts, 
  createProduct, 
  updateProduct, 
  deleteProduct, 
  getProductById 
} from '../controllers/productController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/products - 获取成品列表
router.get('/', getProducts);

// GET /api/products/:id - 获取单个成品详情
router.get('/:id', getProductById);

// POST /api/products - 创建成品
router.post('/', createProduct);

// PUT /api/products/:id - 更新成品
router.put('/:id', updateProduct);

// DELETE /api/products/:id - 删除成品
router.delete('/:id', deleteProduct);

export default router;
