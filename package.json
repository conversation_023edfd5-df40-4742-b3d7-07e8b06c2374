{"name": "jjs-erp-system", "version": "1.0.0", "description": "JJS生产管理软件 - ERP进销存管理系统", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:fix": "npm run lint:fix:frontend && npm run lint:fix:backend", "lint:fix:frontend": "cd frontend && npm run lint", "lint:fix:backend": "cd backend && npm run lint:fix", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf dist node_modules/.cache", "clean:backend": "cd backend && rm -rf dist", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend"]}