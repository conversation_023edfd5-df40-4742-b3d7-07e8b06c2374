import { Router } from 'express';
import {
  getMaterials,
  getMaterial,
  createMaterial,
  updateMaterial,
  deleteMaterial
} from '../controllers/materialController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取原材料列表
router.get('/', getMaterials);

// 获取单个原材料
router.get('/:id', getMaterial);

// 创建原材料
router.post('/', createMaterial);

// 更新原材料
router.put('/:id', updateMaterial);

// 删除原材料
router.delete('/:id', deleteMaterial);

export default router;
