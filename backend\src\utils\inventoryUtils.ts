import { getDatabase } from '../models/database';
import { InventoryAlertCreateInput } from '../types';

// 检查库存预警
export async function checkInventoryAlerts(itemType: 'material' | 'product', itemId: number): Promise<void> {
  const db = getDatabase();
  
  try {
    // 获取当前库存信息
    const tableName = itemType === 'material' ? 'materials' : 'products';
    const itemQuery = `
      SELECT id, code, name, current_stock, stock_min, stock_max 
      FROM ${tableName} 
      WHERE id = ? AND status = 'active'
    `;
    
    const item = await new Promise<any>((resolve, reject) => {
      db.get(itemQuery, [itemId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    if (!item) return;
    
    const { current_stock, stock_min, stock_max } = item;
    
    // 确定预警类型
    let alertType: 'low_stock' | 'high_stock' | 'zero_stock' | null = null;
    let thresholdValue: number | null = null;
    
    if (current_stock === 0) {
      alertType = 'zero_stock';
    } else if (stock_min > 0 && current_stock < stock_min) {
      alertType = 'low_stock';
      thresholdValue = stock_min;
    } else if (stock_max > 0 && current_stock > stock_max) {
      alertType = 'high_stock';
      thresholdValue = stock_max;
    }
    
    if (alertType) {
      // 检查是否已存在相同的活跃预警
      const existingAlertQuery = `
        SELECT id FROM inventory_alerts 
        WHERE item_type = ? AND item_id = ? AND alert_type = ? AND status = 'active'
      `;
      
      const existingAlert = await new Promise<any>((resolve, reject) => {
        db.get(existingAlertQuery, [itemType, itemId, alertType], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });
      
      if (!existingAlert) {
        // 创建新的预警记录
        const alertData: InventoryAlertCreateInput = {
          item_type: itemType,
          item_id: itemId,
          alert_type: alertType,
          current_stock: current_stock,
          threshold_value: thresholdValue || undefined
        };
        
        await createInventoryAlert(alertData);
      }
    } else {
      // 库存正常，解决相关的活跃预警
      await resolveInventoryAlerts(itemType, itemId);
    }
    
  } catch (error) {
    console.error('检查库存预警失败:', error);
  }
}

// 创建库存预警记录
async function createInventoryAlert(alertData: InventoryAlertCreateInput): Promise<number> {
  const db = getDatabase();
  
  const sql = `
    INSERT INTO inventory_alerts (
      item_type, item_id, alert_type, current_stock, threshold_value
    ) VALUES (?, ?, ?, ?, ?)
  `;
  
  const params = [
    alertData.item_type,
    alertData.item_id,
    alertData.alert_type,
    alertData.current_stock,
    alertData.threshold_value || null
  ];
  
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this.lastID);
    });
  });
}

// 解决库存预警（当库存恢复正常时）
export async function resolveInventoryAlerts(itemType: 'material' | 'product', itemId: number): Promise<void> {
  const db = getDatabase();
  
  const sql = `
    UPDATE inventory_alerts 
    SET status = 'resolved', resolved_at = CURRENT_TIMESTAMP
    WHERE item_type = ? AND item_id = ? AND status = 'active'
  `;
  
  return new Promise((resolve, reject) => {
    db.run(sql, [itemType, itemId], (err) => {
      if (err) reject(err);
      else resolve();
    });
  });
}

// 批量检查所有库存预警
export async function checkAllInventoryAlerts(): Promise<void> {
  const db = getDatabase();
  
  try {
    // 检查所有原材料
    const materialsQuery = `
      SELECT id FROM materials WHERE status = 'active'
    `;
    
    const materials = await new Promise<any[]>((resolve, reject) => {
      db.all(materialsQuery, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    for (const material of materials) {
      await checkInventoryAlerts('material', material.id);
    }
    
    // 检查所有成品
    const productsQuery = `
      SELECT id FROM products WHERE status = 'active'
    `;
    
    const products = await new Promise<any[]>((resolve, reject) => {
      db.all(productsQuery, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    for (const product of products) {
      await checkInventoryAlerts('product', product.id);
    }

  } catch (error) {
    console.error('批量检查库存预警失败:', error);
  }
}

// 更新库存并记录变动
export async function updateInventoryWithMovement(
  itemType: 'material' | 'product',
  itemId: number,
  quantity: number,
  movementType: 'in' | 'out' | 'adjust',
  referenceType?: string,
  referenceId?: number,
  referenceNo?: string,
  remark?: string,
  createdBy?: number,
  useTransaction: boolean = true
): Promise<void> {
  const db = getDatabase();

  return new Promise((resolve, reject) => {
    const executeOperations = () => {
      try {
        // 获取当前库存
        const tableName = itemType === 'material' ? 'materials' : 'products';
        const getCurrentStockQuery = `SELECT current_stock FROM ${tableName} WHERE id = ?`;
        
        db.get(getCurrentStockQuery, [itemId], (err, row: any) => {
          if (err) {
            if (useTransaction) db.run('ROLLBACK');
            reject(err);
            return;
          }
          
          const beforeQuantity = row?.current_stock || 0;
          let afterQuantity: number;
          
          // 计算新库存
          if (movementType === 'in') {
            afterQuantity = beforeQuantity + quantity;
          } else if (movementType === 'out') {
            afterQuantity = beforeQuantity - quantity;
          } else { // adjust
            afterQuantity = quantity; // 调整时quantity就是目标库存
          }
          
          // 更新库存
          const updateStockQuery = `UPDATE ${tableName} SET current_stock = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
          
          db.run(updateStockQuery, [afterQuantity, itemId], (err) => {
            if (err) {
              if (useTransaction) db.run('ROLLBACK');
              reject(err);
              return;
            }
            
            // 记录库存变动
            const insertMovementQuery = `
              INSERT INTO inventory_movements (
                item_type, item_id, movement_type, quantity, before_quantity, 
                after_quantity, reference_type, reference_id, reference_no, 
                remark, created_by
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            const actualQuantity = movementType === 'adjust' ? (afterQuantity - beforeQuantity) : quantity;
            
            db.run(insertMovementQuery, [
              itemType, itemId, movementType, actualQuantity, beforeQuantity,
              afterQuantity, referenceType || null, referenceId || null,
              referenceNo || null, remark || null, createdBy || null
            ], (err) => {
              if (err) {
                if (useTransaction) db.run('ROLLBACK');
                reject(err);
                return;
              }

              if (useTransaction) {
                db.run('COMMIT', (err) => {
                  if (err) {
                    db.run('ROLLBACK');
                    reject(err);
                  } else {
                    // 异步检查预警（不阻塞主流程）
                    checkInventoryAlerts(itemType, itemId).catch(console.error);

                    // 清除相关缓存（如果有缓存系统）
                    // 这里可以添加缓存清除逻辑

                    resolve();
                  }
                });
              } else {
                // 异步检查预警（不阻塞主流程）
                checkInventoryAlerts(itemType, itemId).catch(console.error);
                resolve();
              }
            });
          });
        });

      } catch (error) {
        if (useTransaction) db.run('ROLLBACK');
        reject(error);
      }
    };

    if (useTransaction) {
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');
        executeOperations();
      });
    } else {
      executeOperations();
    }
  });
}
