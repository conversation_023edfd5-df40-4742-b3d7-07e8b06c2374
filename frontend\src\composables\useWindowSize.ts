import { ref, computed, onMounted, onUnmounted } from 'vue'

// 全局窗口大小状态
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1920)
const windowHeight = ref(typeof window !== 'undefined' ? window.innerHeight : 1080)

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: number | undefined
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 全局resize处理器
const handleResize = debounce(() => {
  windowWidth.value = window.innerWidth
  windowHeight.value = window.innerHeight
}, 100)

// 是否已经初始化
let initialized = false

export function useWindowSize() {
  // 响应式断点
  const isMobile = computed(() => windowWidth.value < 768)
  const isTablet = computed(() => windowWidth.value >= 768 && windowWidth.value < 1024)
  const isDesktop = computed(() => windowWidth.value >= 1024 && windowWidth.value < 1400)
  const isLargeScreen = computed(() => windowWidth.value >= 1400)

  // 侧边栏宽度计算
  const sidebarWidth = computed(() => {
    if (isMobile.value) return '250px'
    if (isTablet.value) return '200px'
    return '200px'
  })

  // 初始化事件监听器（只初始化一次）
  onMounted(() => {
    if (!initialized && typeof window !== 'undefined') {
      // 立即更新一次
      windowWidth.value = window.innerWidth
      windowHeight.value = window.innerHeight
      
      // 添加事件监听器
      window.addEventListener('resize', handleResize)
      initialized = true
    }
  })

  // 清理事件监听器
  onUnmounted(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', handleResize)
    }
  })

  return {
    windowWidth: readonly(windowWidth),
    windowHeight: readonly(windowHeight),
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    sidebarWidth
  }
}

// 导出只读的响应式引用
function readonly<T>(ref: any) {
  return computed(() => ref.value)
}
