import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import { Material, MaterialCreateInput, MaterialUpdateInput, ApiResponse, PaginatedResponse } from '../types';
import { checkInventoryAlerts } from '../utils/inventoryUtils';

// 获取原材料列表
export async function getMaterials(req: Request, res: Response) {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    
    const db = getDatabase();
    
    // 构建查询条件
    let whereClause = "WHERE status = 'active'";
    const params: any[] = [];
    
    if (search) {
      whereClause += " AND (code LIKE ? OR name LIKE ? OR specification LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    
    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM materials ${whereClause}`;
    const totalResult = await new Promise<{ total: number }>((resolve, reject) => {
      db.get(countQuery, params, (err, row) => {
        if (err) reject(err);
        else resolve(row as { total: number });
      });
    });
    
    // 获取数据
    const dataQuery = `
      SELECT * FROM materials ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    const materials = await new Promise<Material[]>((resolve, reject) => {
      db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
        if (err) reject(err);
        else resolve(rows as Material[]);
      });
    });
    
    const response: PaginatedResponse<Material> = {
      data: materials,
      total: totalResult.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(totalResult.total / Number(limit))
    };
    
    res.json({
      success: true,
      message: '获取原材料列表成功',
      data: response
    } as ApiResponse<PaginatedResponse<Material>>);
    
  } catch (error) {
    console.error('获取原材料列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 获取单个原材料
export async function getMaterial(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const db = getDatabase();
    
    const material = await new Promise<Material | undefined>((resolve, reject) => {
      db.get('SELECT * FROM materials WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row as Material);
      });
    });
    
    if (!material) {
      return res.status(404).json({
        success: false,
        message: '原材料不存在'
      } as ApiResponse);
    }
    
    res.json({
      success: true,
      message: '获取原材料成功',
      data: material
    } as ApiResponse<Material>);
    
  } catch (error) {
    console.error('获取原材料错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 创建原材料
export async function createMaterial(req: Request, res: Response) {
  try {
    const {
      code,
      name,
      specification = '',
      unit,
      cost_price = 0,
      stock_min = 0,
      stock_max = 0,
      current_stock = 0
    }: MaterialCreateInput = req.body;
    
    // 验证必填字段
    if (!code || !name || !unit) {
      return res.status(400).json({
        success: false,
        message: '编码、名称和单位不能为空'
      } as ApiResponse);
    }
    
    const db = getDatabase();
    
    // 检查编码是否已存在
    const existingMaterial = await new Promise<Material | undefined>((resolve, reject) => {
      db.get('SELECT * FROM materials WHERE code = ?', [code], (err, row) => {
        if (err) reject(err);
        else resolve(row as Material);
      });
    });
    
    if (existingMaterial) {
      return res.status(409).json({
        success: false,
        message: '原材料编码已存在'
      } as ApiResponse);
    }
    
    // 创建原材料
    const materialId = await new Promise<number>((resolve, reject) => {
      db.run(
        `INSERT INTO materials (code, name, specification, unit, cost_price, stock_min, stock_max, current_stock)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [code, name, specification, unit, cost_price, stock_min, stock_max, current_stock],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });

    // 检查库存预警
    await checkInventoryAlerts('material', materialId);

    res.status(201).json({
      success: true,
      message: '原材料创建成功',
      data: { id: materialId }
    } as ApiResponse);
    
  } catch (error) {
    console.error('创建原材料错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 更新原材料
export async function updateMaterial(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const updateData: MaterialUpdateInput = req.body;
    
    const db = getDatabase();
    
    // 检查原材料是否存在
    const existingMaterial = await new Promise<Material | undefined>((resolve, reject) => {
      db.get('SELECT * FROM materials WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row as Material);
      });
    });
    
    if (!existingMaterial) {
      return res.status(404).json({
        success: false,
        message: '原材料不存在'
      } as ApiResponse);
    }
    
    // 如果更新编码，检查是否重复
    if (updateData.code && updateData.code !== existingMaterial.code) {
      const codeExists = await new Promise<Material | undefined>((resolve, reject) => {
        db.get('SELECT * FROM materials WHERE code = ? AND id != ?', [updateData.code, id], (err, row) => {
          if (err) reject(err);
          else resolve(row as Material);
        });
      });
      
      if (codeExists) {
        return res.status(409).json({
          success: false,
          message: '原材料编码已存在'
        } as ApiResponse);
      }
    }
    
    // 构建更新语句
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有提供更新数据'
      } as ApiResponse);
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);
    
    await new Promise<void>((resolve, reject) => {
      db.run(
        `UPDATE materials SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues,
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // 检查库存预警
    await checkInventoryAlerts('material', parseInt(id));

    res.json({
      success: true,
      message: '原材料更新成功'
    } as ApiResponse);
    
  } catch (error) {
    console.error('更新原材料错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 删除原材料（软删除）
export async function deleteMaterial(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const db = getDatabase();
    
    // 检查原材料是否存在
    const existingMaterial = await new Promise<Material | undefined>((resolve, reject) => {
      db.get('SELECT * FROM materials WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row as Material);
      });
    });
    
    if (!existingMaterial) {
      return res.status(404).json({
        success: false,
        message: '原材料不存在'
      } as ApiResponse);
    }
    
    // 软删除（设置状态为inactive）
    await new Promise<void>((resolve, reject) => {
      db.run(
        'UPDATE materials SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['inactive', id],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
    
    res.json({
      success: true,
      message: '原材料删除成功'
    } as ApiResponse);
    
  } catch (error) {
    console.error('删除原材料错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}
