import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { getDatabase } from '../models/database';
import { User, UserCreateInput, UserLoginInput, ApiResponse } from '../types';

// 用户注册
export async function register(req: Request, res: Response) {
  try {
    const { username, password, email, role = 'user' }: UserCreateInput = req.body;

    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      } as ApiResponse);
    }

    // 验证用户名长度
    if (username.length < 3 || username.length > 50) {
      return res.status(400).json({
        success: false,
        message: '用户名长度必须在3-50个字符之间'
      } as ApiResponse);
    }

    // 验证密码长度
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: '密码长度不能少于6个字符'
      } as ApiResponse);
    }

    const db = getDatabase();

    // 检查用户名是否已存在
    const existingUser = await new Promise<User | undefined>((resolve, reject) => {
      db.get(
        'SELECT * FROM users WHERE username = ?',
        [username],
        (err, row) => {
          if (err) reject(err);
          else resolve(row as User);
        }
      );
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: '用户名已存在'
      } as ApiResponse);
    }

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const userId = await new Promise<number>((resolve, reject) => {
      db.run(
        `INSERT INTO users (username, password, email, role) 
         VALUES (?, ?, ?, ?)`,
        [username, hashedPassword, email || null, role],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });

    res.status(201).json({
      success: true,
      message: '用户注册成功',
      data: {
        id: userId,
        username,
        email,
        role
      }
    } as ApiResponse);

  } catch (error) {
    console.error('用户注册错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 用户登录
export async function login(req: Request, res: Response) {
  try {
    const { username, password }: UserLoginInput = req.body;

    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      } as ApiResponse);
    }

    const db = getDatabase();

    // 查找用户
    const user = await new Promise<User | undefined>((resolve, reject) => {
      db.get(
        'SELECT * FROM users WHERE username = ?',
        [username],
        (err, row) => {
          if (err) reject(err);
          else resolve(row as User);
        }
      );
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      } as ApiResponse);
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      } as ApiResponse);
    }

    // 生成JWT令牌
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      return res.status(500).json({
        success: false,
        message: '服务器配置错误'
      } as ApiResponse);
    }

    const tokenPayload = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };

    const token = jwt.sign(
      tokenPayload,
      jwtSecret,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' } as jwt.SignOptions
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role
        }
      }
    } as ApiResponse);

  } catch (error) {
    console.error('用户登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 获取当前用户信息
export async function getCurrentUser(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      } as ApiResponse);
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      data: req.user
    } as ApiResponse);

  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}
