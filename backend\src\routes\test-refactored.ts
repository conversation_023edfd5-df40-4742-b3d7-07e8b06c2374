import { Router } from 'express';
import {
  createMaterialNew,
  updateMaterialNew,
  deleteMaterialNew,
  getMaterialNew
} from '../controllers/materialController';
import {
  createProductNew,
  updateProductNew,
  deleteProductNew,
  getProductNew
} from '../controllers/productController';
import {
  createCustomerNew,
  updateCustomerNew,
  deleteCustomerNew,
  getCustomerNew
} from '../controllers/customerController';
import {
  createSupplierNew,
  updateSupplierNew,
  deleteSupplierNew,
  getSupplierNew
} from '../controllers/supplierController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 原材料重构后的路由
router.post('/materials', createMaterialNew);
router.put('/materials/:id', updateMaterialNew);
router.delete('/materials/:id', deleteMaterialNew);
router.get('/materials/:id', getMaterialNew);

// 产品重构后的路由
router.post('/products', createProductNew);
router.put('/products/:id', updateProductNew);
router.delete('/products/:id', deleteProductNew);
router.get('/products/:id', getProductNew);

// 客户重构后的路由
router.post('/customers', createCustomerNew);
router.put('/customers/:id', updateCustomerNew);
router.delete('/customers/:id', deleteCustomerNew);
router.get('/customers/:id', getCustomerNew);

// 供应商重构后的路由
router.post('/suppliers', createSupplierNew);
router.put('/suppliers/:id', updateSupplierNew);
router.delete('/suppliers/:id', deleteSupplierNew);
router.get('/suppliers/:id', getSupplierNew);

export default router;
